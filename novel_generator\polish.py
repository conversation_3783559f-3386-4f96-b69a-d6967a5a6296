# novel_generator/polish.py
# -*- coding: utf-8 -*-
"""
AI小说润色模块 - 去AI味处理
提供多层次润色策略，使AI生成的内容更符合人类作者的写作风格
"""
import os
import re
import logging
import traceback
from typing import Dict, List, Optional, Tuple
from llm_adapters import create_llm_adapter
from novel_generator.common import invoke_with_cleaning
from utils import read_file, save_string_to_txt, clear_file_content

class PolishLevel:
    """润色级别常量"""
    LIGHT = "light"      # 轻度润色：主要修正语言表达
    MEDIUM = "medium"    # 中度润色：改善表达+增加细节
    DEEP = "deep"        # 深度润色：全面重写+风格统一

class AITraitDetector:
    """AI味特征检测器"""

    # AI生成文本的常见特征模式
    AI_PATTERNS = [
        r'突然[，,]',  # 过度使用"突然"
        r'然而[，,]',  # 过度使用"然而"
        r'与此同时[，,]',  # 机械化的时间连接
        r'不禁[想到|感到|觉得]',  # 固定搭配过多
        r'心中[涌起|升起|产生]',  # 情感描述模式化
        r'[眼中|脸上]闪过',  # 表情描述套路化
        r'仿佛[看到|听到|感受到]',  # 过度使用比喻
        r'这[一刻|瞬间|时候]',  # 时间表达重复
        r'[深深地|紧紧地|静静地]',  # 副词使用过度
        r'[的确|确实|毫无疑问]',  # 确定性表达过多
    ]

    @classmethod
    def detect_ai_traits(cls, text: str) -> Dict[str, int]:
        """
        检测文本中的AI味特征
        返回: {特征名: 出现次数}
        """
        traits = {}
        for pattern in cls.AI_PATTERNS:
            matches = re.findall(pattern, text)
            if matches:
                traits[pattern] = len(matches)
        return traits

    @classmethod
    def calculate_ai_score(cls, text: str) -> float:
        """
        计算文本的AI味评分 (0-1, 越高越像AI生成)
        """
        traits = cls.detect_ai_traits(text)
        total_traits = sum(traits.values())
        text_length = len(text)

        if text_length == 0:
            return 0.0

        # 基于特征密度计算评分
        density = total_traits / (text_length / 100)  # 每100字的特征数
        score = min(density / 5.0, 1.0)  # 归一化到0-1
        return score

class StyleAnalyzer:
    """写作风格分析器"""

    @staticmethod
    def analyze_sentence_patterns(text: str) -> Dict[str, float]:
        """分析句式模式"""
        sentences = re.split(r'[。！？]', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        if not sentences:
            return {}

        total = len(sentences)
        patterns = {
            'short_sentences': 0,    # 短句比例
            'long_sentences': 0,     # 长句比例
            'question_sentences': 0, # 疑问句比例
            'exclamation_sentences': 0, # 感叹句比例
        }

        for sentence in sentences:
            length = len(sentence)
            if length < 15:
                patterns['short_sentences'] += 1
            elif length > 40:
                patterns['long_sentences'] += 1

            if '？' in sentence or '吗' in sentence or '呢' in sentence:
                patterns['question_sentences'] += 1
            if '！' in sentence:
                patterns['exclamation_sentences'] += 1

        # 转换为比例
        for key in patterns:
            patterns[key] = patterns[key] / total

        return patterns

    @staticmethod
    def extract_vocabulary_features(text: str) -> Dict[str, int]:
        """提取词汇特征"""
        features = {
            'adjective_count': len(re.findall(r'[的地得][^，。！？]*?[的]', text)),
            'adverb_count': len(re.findall(r'[地][^，。！？]*?[地]', text)),
            'metaphor_count': len(re.findall(r'[如像仿佛好似]', text)),
            'dialogue_count': len(re.findall(r'[""].*?[""]', text)),
        }
        return features

def polish_text_content(
    interface_format: str,
    api_key: str,
    base_url: str,
    llm_model: str,
    content: str,
    polish_level: str = PolishLevel.MEDIUM,
    style_reference: str = "",
    character_info: str = "",
    scene_context: str = "",
    user_guidance: str = "",
    temperature: float = 0.7,
    max_tokens: int = 4096,
    timeout: int = 600
) -> Tuple[str, Dict]:
    """
    对文本内容进行去AI味润色

    Args:
        interface_format: LLM接口格式
        api_key: API密钥
        base_url: API基础URL
        llm_model: 模型名称
        content: 待润色的内容
        polish_level: 润色级别 (light/medium/deep)
        style_reference: 风格参考文本
        character_info: 角色信息
        scene_context: 场景上下文
        user_guidance: 用户指导
        temperature: 温度参数
        max_tokens: 最大token数
        timeout: 超时时间

    Returns:
        Tuple[润色后的内容, 分析报告]
    """

    # 注意：专用润色模型配置现在在UI层面统一处理
    # 这里直接使用传入的参数，这些参数已经在UI层面根据配置进行了选择

    # 创建LLM适配器
    llm_adapter = create_llm_adapter(
        interface_format=interface_format,
        base_url=base_url,
        model_name=llm_model,
        api_key=api_key,
        temperature=temperature,
        max_tokens=max_tokens,
        timeout=timeout
    )

    # 分析原文特征
    ai_score = AITraitDetector.calculate_ai_score(content)
    ai_traits = AITraitDetector.detect_ai_traits(content)
    style_patterns = StyleAnalyzer.analyze_sentence_patterns(content)
    vocab_features = StyleAnalyzer.extract_vocabulary_features(content)

    # 构建润色提示词
    try:
        from prompt_definitions import get_polish_prompt
    except ImportError:
        # 如果导入失败，使用基本的提示词
        def get_polish_prompt(**kwargs):
            return f"""请对以下文本进行润色，使其更符合人类作者的写作风格：

原文内容：
{kwargs.get('content', '')}

润色级别：{kwargs.get('polish_level', 'medium')}

请输出润色后的内容："""

    prompt = get_polish_prompt(
        content=content,
        polish_level=polish_level,
        style_reference=style_reference,
        character_info=character_info,
        scene_context=scene_context,
        user_guidance=user_guidance,
        ai_score=ai_score,
        ai_traits=ai_traits
    )

    # 执行润色
    try:
        polished_content = invoke_with_cleaning(llm_adapter, prompt)

        # 分析润色效果
        new_ai_score = AITraitDetector.calculate_ai_score(polished_content)
        improvement = ai_score - new_ai_score

        analysis_report = {
            'original_ai_score': ai_score,
            'polished_ai_score': new_ai_score,
            'improvement': improvement,
            'original_length': len(content),
            'polished_length': len(polished_content),
            'style_patterns': style_patterns,
            'vocab_features': vocab_features,
            'polish_level': polish_level
        }

        return polished_content, analysis_report

    except Exception as e:
        logging.error(f"润色过程出错: {e}")
        traceback.print_exc()
        return content, {'error': str(e)}

def polish_chapter_file(
    interface_format: str,
    api_key: str,
    base_url: str,
    llm_model: str,
    chapter_file_path: str,
    output_path: str = None,
    polish_level: str = PolishLevel.MEDIUM,
    style_reference_file: str = "",
    character_state_file: str = "",
    **kwargs
) -> bool:
    """
    润色章节文件

    Args:
        chapter_file_path: 章节文件路径
        output_path: 输出路径，如果为None则覆盖原文件
        其他参数同polish_text_content

    Returns:
        bool: 是否成功
    """
    try:
        # 读取章节内容
        if not os.path.exists(chapter_file_path):
            logging.error(f"章节文件不存在: {chapter_file_path}")
            return False

        content = read_file(chapter_file_path)
        if not content.strip():
            logging.warning(f"章节文件为空: {chapter_file_path}")
            return False

        # 读取参考资料
        style_reference = ""
        if style_reference_file and os.path.exists(style_reference_file):
            style_reference = read_file(style_reference_file)

        character_info = ""
        if character_state_file and os.path.exists(character_state_file):
            character_info = read_file(character_state_file)

        # 执行润色
        polished_content, analysis_report = polish_text_content(
            interface_format=interface_format,
            api_key=api_key,
            base_url=base_url,
            llm_model=llm_model,
            content=content,
            polish_level=polish_level,
            style_reference=style_reference,
            character_info=character_info,
            **kwargs
        )

        # 保存结果
        if output_path is None:
            output_path = chapter_file_path

        # 备份原文件
        if output_path == chapter_file_path:
            backup_path = chapter_file_path + ".backup"
            save_string_to_txt(content, backup_path)
            logging.info(f"原文件已备份到: {backup_path}")

        # 保存润色后的内容
        save_string_to_txt(polished_content, output_path)

        # 保存分析报告
        report_path = output_path.replace('.txt', '_polish_report.json')
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, ensure_ascii=False, indent=2)

        logging.info(f"章节润色完成: {output_path}")
        logging.info(f"分析报告保存到: {report_path}")

        return True

    except Exception as e:
        logging.error(f"润色章节文件时出错: {e}")
        traceback.print_exc()
        return False

def batch_polish_chapters(
    interface_format: str,
    api_key: str,
    base_url: str,
    llm_model: str,
    chapters_dir: str,
    chapter_numbers: List[int],
    polish_level: str = PolishLevel.MEDIUM,
    **kwargs
) -> Dict[int, bool]:
    """
    批量润色章节

    Args:
        chapters_dir: 章节目录
        chapter_numbers: 要润色的章节号列表
        其他参数同polish_text_content

    Returns:
        Dict[章节号, 是否成功]
    """
    results = {}

    for chapter_num in chapter_numbers:
        chapter_file = os.path.join(chapters_dir, f"chapter_{chapter_num}.txt")

        logging.info(f"开始润色第{chapter_num}章...")

        success = polish_chapter_file(
            interface_format=interface_format,
            api_key=api_key,
            base_url=base_url,
            llm_model=llm_model,
            chapter_file_path=chapter_file,
            polish_level=polish_level,
            **kwargs
        )

        results[chapter_num] = success

        if success:
            logging.info(f"第{chapter_num}章润色成功")
        else:
            logging.error(f"第{chapter_num}章润色失败")

    return results