# 润色提示词配置系统

## 📁 文件结构

```
prompts/
├── README.md           # 本说明文件
├── polish_light.txt    # 轻度润色提示词
├── polish_medium.txt   # 中度润色提示词
└── polish_deep.txt     # 深度润色提示词
```

## 🎯 功能说明

本系统允许用户自定义润色提示词，无需修改代码即可调整润色策略。

### 润色级别

- **轻度润色** (`polish_light.txt`): 主要修正语言表达问题，保持95%以上原文不变
- **中度润色** (`polish_medium.txt`): 改善表达并增加细节，推荐使用
- **深度润色** (`polish_deep.txt`): 全面重写以达到人类作者水准

## 🔧 使用方法

### 自定义提示词

1. 直接编辑对应的 `.txt` 文件
2. 保存文件后，程序会自动使用新的提示词
3. 无需重启程序，下次润色时即生效

### 占位符语法

提示词文件中可以使用以下占位符：

- `{content}` - 待润色的原文内容
- `{character_info}` - 角色信息
- `{scene_context}` - 场景上下文
- `{style_reference}` - 风格参考文本
- `{user_guidance}` - 用户指导
- `{ai_score:.2f}` - AI味评分（保留2位小数）
- `{ai_traits_text}` - AI特征分析文本

### 示例

```
作为专业编辑，请对以下文本进行润色：

【原文内容】
{content}

【参考信息】
角色信息：{character_info}
用户指导：{user_guidance}

请直接输出润色后的文本，不要添加任何解释：
```

## ⚠️ 注意事项

1. **文件编码**: 请使用 UTF-8 编码保存文件
2. **占位符**: 确保占位符语法正确，否则可能导致格式化错误
3. **备份**: 建议在修改前备份原始提示词文件
4. **测试**: 修改后建议先用简短文本测试效果

## 🔄 错误处理

- 如果文件不存在，程序会使用内置的默认提示词
- 如果文件为空或读取失败，会显示警告并使用默认提示词
- 程序会在控制台输出相关警告信息

## 💡 优化建议

1. **明确指令**: 在提示词末尾明确要求只输出润色后的文本
2. **保持格式**: 要求保持原文的排版和格式
3. **避免解释**: 明确禁止添加解释、说明或格式标记
4. **针对性**: 根据不同润色级别调整提示词的详细程度

## 🚀 高级用法

### 创建自定义润色级别

虽然系统默认支持三个级别，但您可以：

1. 修改现有文件来创建自定义的润色策略
2. 针对特定类型的文本（如对话、描述、动作）创建专门的提示词
3. 根据不同的文学风格调整提示词内容

### 批量测试

建议在大规模使用前：

1. 准备多个测试文本样本
2. 逐一测试修改后的提示词效果
3. 根据结果进一步优化提示词内容
