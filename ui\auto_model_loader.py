# ui/auto_model_loader.py
# -*- coding: utf-8 -*-
"""
自动模型加载器 - 为AI小说生成器提供智能模型加载功能
"""
import threading
import time
import re
from typing import Callable, Optional, Dict, Any
from tkinter import messagebox


class AutoModelLoader:
    """自动模型加载器类"""
    
    def __init__(self, 
                 get_models_func: Callable,
                 update_ui_func: Callable,
                 show_status_func: Optional[Callable] = None,
                 show_error_func: Optional[Callable] = None):
        """
        初始化自动加载器
        
        Args:
            get_models_func: 获取模型列表的函数
            update_ui_func: 更新UI的函数
            show_status_func: 显示状态的函数（可选）
            show_error_func: 显示错误的函数（可选）
        """
        self.get_models_func = get_models_func
        self.update_ui_func = update_ui_func
        self.show_status_func = show_status_func
        self.show_error_func = show_error_func
        
        # 防抖动相关
        self.debounce_timer = None
        self.debounce_delay = 1.5  # 1.5秒防抖动延迟
        
        # 缓存机制
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存过期
        
        # 加载状态
        self.is_loading = False
        self.last_load_time = 0
        
    def _generate_cache_key(self, interface: str, api_key: str, base_url: str) -> str:
        """生成缓存键"""
        # 对API密钥进行部分遮蔽以保护隐私
        masked_key = api_key[:8] + "***" if len(api_key) > 8 else "***"
        return f"{interface}:{masked_key}:{base_url}"
    
    def _is_valid_config(self, interface: str, api_key: str, base_url: str) -> bool:
        """验证配置是否有效"""
        if not interface or not api_key:
            return False
            
        # 基本的API密钥格式验证
        if len(api_key.strip()) < 8:
            return False
            
        # 基本的URL格式验证（如果提供了URL）
        if base_url and base_url.strip():
            url_pattern = r'^https?://'
            if not re.match(url_pattern, base_url.strip()):
                return False
                
        return True
    
    def _get_cached_models(self, cache_key: str) -> Optional[list]:
        """获取缓存的模型列表"""
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            # 检查缓存是否过期
            if time.time() - cached_data['timestamp'] < self.cache_timeout:
                return cached_data['models']
            else:
                # 清除过期缓存
                del self.cache[cache_key]
        return None
    
    def _cache_models(self, cache_key: str, models: list):
        """缓存模型列表"""
        self.cache[cache_key] = {
            'models': models,
            'timestamp': time.time()
        }
    
    def _load_models_async(self, interface: str, api_key: str, base_url: str):
        """异步加载模型"""
        try:
            self.is_loading = True

            # 显示加载状态
            if self.show_status_func:
                self.show_status_func("加载中...")

            # 生成缓存键
            cache_key = self._generate_cache_key(interface, api_key, base_url)

            # 检查缓存
            cached_models = self._get_cached_models(cache_key)
            if cached_models:
                # 使用缓存的模型
                self.update_ui_func(cached_models, from_cache=True)
                return

            # 调用获取模型的函数
            models = self.get_models_func(interface, api_key, base_url)

            if models and len(models) > 0:
                # 缓存结果
                self._cache_models(cache_key, models)

                # 更新UI
                self.update_ui_func(models, from_cache=False)
            else:
                # 没有找到模型
                if self.show_error_func:
                    self.show_error_func("未找到可用模型")
                elif self.show_status_func:
                    self.show_status_func("未找到模型")

        except Exception as e:
            # 处理错误
            error_msg = f"加载失败: {str(e)[:50]}"
            if self.show_error_func:
                self.show_error_func(error_msg)
            elif self.show_status_func:
                self.show_status_func("加载失败")

        finally:
            self.is_loading = False
            self.last_load_time = time.time()
    
    def trigger_auto_load(self, interface: str, api_key: str, base_url: str):
        """触发自动加载（带防抖动）"""
        # 取消之前的定时器
        if self.debounce_timer:
            self.debounce_timer.cancel()
        
        # 验证配置
        if not self._is_valid_config(interface, api_key, base_url):
            return
        
        # 检查是否正在加载
        if self.is_loading:
            return
            
        # 检查是否过于频繁
        if time.time() - self.last_load_time < 2:  # 2秒内不重复加载
            return
        
        # 设置新的定时器
        self.debounce_timer = threading.Timer(
            self.debounce_delay,
            lambda: threading.Thread(
                target=self._load_models_async,
                args=(interface, api_key, base_url),
                daemon=True
            ).start()
        )
        self.debounce_timer.start()
    
    def manual_load(self, interface: str, api_key: str, base_url: str):
        """手动加载（立即执行）"""
        if self.is_loading:
            return False
            
        if not self._is_valid_config(interface, api_key, base_url):
            if self.show_error_func:
                self.show_error_func("请检查接口格式和API密钥配置")
            return False
        
        # 立即执行加载
        threading.Thread(
            target=self._load_models_async,
            args=(interface, api_key, base_url),
            daemon=True
        ).start()
        return True
    
    def clear_cache(self):
        """清除缓存"""
        self.cache.clear()
    
    def is_loading_models(self) -> bool:
        """检查是否正在加载模型"""
        return self.is_loading


class ModelLoadingStatus:
    """模型加载状态管理器"""

    def __init__(self, status_label=None, load_button=None, button_text="加载模型"):
        self.load_button = load_button
        self.original_button_text = button_text

        # 如果按钮已存在，尝试获取其文字
        if load_button:
            try:
                self.original_button_text = load_button.cget("text")
            except:
                self.original_button_text = button_text

    def set_button(self, button, button_text="加载模型"):
        """设置按钮引用"""
        self.load_button = button
        try:
            self.original_button_text = button.cget("text")
        except:
            self.original_button_text = button_text

    def show_loading(self):
        """显示加载状态"""
        if self.load_button:
            self.load_button.configure(text="加载中...", state="disabled")

    def show_success(self, count: int, from_cache: bool = False):
        """显示成功状态"""
        if self.load_button:
            self.load_button.configure(text=self.original_button_text, state="normal")

    def show_error(self, error_msg: str):
        """显示错误状态"""
        if self.load_button:
            self.load_button.configure(text=self.original_button_text, state="normal")

    def clear_status(self):
        """清除状态"""
        if self.load_button:
            self.load_button.configure(text=self.original_button_text, state="normal")
