#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
润色功能测试脚本 - 简化版
用于验证润色模块的基本功能，不依赖外部库
"""

import sys
import os
import re

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_trait_detector():
    """测试AI味检测器"""
    print("=== 测试AI味检测器 ===")

    try:
        # 直接导入类，避免依赖问题
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'novel_generator'))

        from polish import AITraitDetector

        # 测试文本（包含典型的AI生成特征）
        test_text = """突然，他心中涌起了一阵不安。然而，与此同时，他不禁想到了那个神秘的女子。
她的眼中闪过一丝复杂的情感，仿佛看到了什么不可思议的事情。这一刻，他深深地感受到了命运的安排。
的确，这一切都太过巧合了。毫无疑问，这背后隐藏着什么秘密。"""

        # 检测AI特征
        traits = AITraitDetector.detect_ai_traits(test_text)
        ai_score = AITraitDetector.calculate_ai_score(test_text)

        print(f"测试文本: {test_text}")
        print(f"检测到的AI特征: {traits}")
        print(f"AI味评分: {ai_score:.3f}/1.0")

        if ai_score > 0.3:
            print("✅ AI味检测器工作正常 - 成功检测到AI特征")
        else:
            print("⚠️ AI味检测器可能需要调整 - 未检测到明显AI特征")

        return True

    except Exception as e:
        print(f"❌ AI味检测器测试失败: {e}")
        return False

def test_style_analyzer():
    """测试风格分析器"""
    print("\n=== 测试风格分析器 ===")

    try:
        from novel_generator.polish import StyleAnalyzer

        test_text = """这是一个测试文本。它包含了不同长度的句子。有些句子很短。
有些句子则相对较长，包含了更多的信息和描述性的内容。
你觉得这样的分析有用吗？当然有用！
"这是一段对话，"他说道，"用来测试对话检测功能。"
"""

        # 分析句式模式
        patterns = StyleAnalyzer.analyze_sentence_patterns(test_text)
        vocab_features = StyleAnalyzer.extract_vocabulary_features(test_text)

        print(f"测试文本: {test_text}")
        print(f"句式模式: {patterns}")
        print(f"词汇特征: {vocab_features}")

        if patterns and vocab_features:
            print("✅ 风格分析器工作正常")
        else:
            print("❌ 风格分析器可能有问题")

        return True

    except Exception as e:
        print(f"❌ 风格分析器测试失败: {e}")
        return False

def test_prompt_generation():
    """测试提示词生成"""
    print("\n=== 测试提示词生成 ===")

    try:
        from prompt_definitions import get_polish_prompt

        test_content = "这是一个测试内容。"

        # 测试不同级别的提示词
        for level in ["light", "medium", "deep"]:
            prompt = get_polish_prompt(
                content=test_content,
                polish_level=level,
                style_reference="参考风格",
                character_info="角色信息",
                scene_context="场景上下文",
                user_guidance="用户指导",
                ai_score=0.5
            )

            print(f"{level}级别提示词长度: {len(prompt)}字符")

        print("✅ 提示词生成功能正常")
        return True

    except Exception as e:
        print(f"❌ 提示词生成测试失败: {e}")
        return False

def test_config_management():
    """测试配置管理"""
    print("\n=== 测试配置管理 ===")

    try:
        from config_manager import (
            get_default_polish_config,
            load_polish_config,
            save_polish_config,
            validate_polish_config
        )

        # 测试默认配置
        default_config = get_default_polish_config()
        print(f"默认配置: {default_config}")

        # 测试配置验证
        is_valid, message = validate_polish_config(default_config)
        print(f"配置验证结果: {is_valid}, 消息: {message}")

        if is_valid:
            print("✅ 配置管理功能正常")
        else:
            print(f"❌ 配置验证失败: {message}")

        return is_valid

    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始润色功能测试...\n")

    test_results = []

    # 运行各项测试
    test_results.append(test_ai_trait_detector())
    test_results.append(test_style_analyzer())
    test_results.append(test_prompt_generation())
    test_results.append(test_config_management())

    # 汇总结果
    passed = sum(test_results)
    total = len(test_results)

    print(f"\n=== 测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")

    if passed == total:
        print("🎉 所有测试通过！润色功能基本可用。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)