# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1CustomResourceSubresourceScale(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'label_selector_path': 'str',
        'spec_replicas_path': 'str',
        'status_replicas_path': 'str'
    }

    attribute_map = {
        'label_selector_path': 'labelSelectorPath',
        'spec_replicas_path': 'specReplicasPath',
        'status_replicas_path': 'statusReplicasPath'
    }

    def __init__(self, label_selector_path=None, spec_replicas_path=None, status_replicas_path=None, local_vars_configuration=None):  # noqa: E501
        """V1CustomResourceSubresourceScale - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._label_selector_path = None
        self._spec_replicas_path = None
        self._status_replicas_path = None
        self.discriminator = None

        if label_selector_path is not None:
            self.label_selector_path = label_selector_path
        self.spec_replicas_path = spec_replicas_path
        self.status_replicas_path = status_replicas_path

    @property
    def label_selector_path(self):
        """Gets the label_selector_path of this V1CustomResourceSubresourceScale.  # noqa: E501

        labelSelectorPath defines the JSON path inside of a custom resource that corresponds to Scale `status.selector`. Only JSON paths without the array notation are allowed. Must be a JSON Path under `.status` or `.spec`. Must be set to work with HorizontalPodAutoscaler. The field pointed by this JSON path must be a string field (not a complex selector struct) which contains a serialized label selector in string form. More info: https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions#scale-subresource If there is no value under the given path in the custom resource, the `status.selector` value in the `/scale` subresource will default to the empty string.  # noqa: E501

        :return: The label_selector_path of this V1CustomResourceSubresourceScale.  # noqa: E501
        :rtype: str
        """
        return self._label_selector_path

    @label_selector_path.setter
    def label_selector_path(self, label_selector_path):
        """Sets the label_selector_path of this V1CustomResourceSubresourceScale.

        labelSelectorPath defines the JSON path inside of a custom resource that corresponds to Scale `status.selector`. Only JSON paths without the array notation are allowed. Must be a JSON Path under `.status` or `.spec`. Must be set to work with HorizontalPodAutoscaler. The field pointed by this JSON path must be a string field (not a complex selector struct) which contains a serialized label selector in string form. More info: https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions#scale-subresource If there is no value under the given path in the custom resource, the `status.selector` value in the `/scale` subresource will default to the empty string.  # noqa: E501

        :param label_selector_path: The label_selector_path of this V1CustomResourceSubresourceScale.  # noqa: E501
        :type: str
        """

        self._label_selector_path = label_selector_path

    @property
    def spec_replicas_path(self):
        """Gets the spec_replicas_path of this V1CustomResourceSubresourceScale.  # noqa: E501

        specReplicasPath defines the JSON path inside of a custom resource that corresponds to Scale `spec.replicas`. Only JSON paths without the array notation are allowed. Must be a JSON Path under `.spec`. If there is no value under the given path in the custom resource, the `/scale` subresource will return an error on GET.  # noqa: E501

        :return: The spec_replicas_path of this V1CustomResourceSubresourceScale.  # noqa: E501
        :rtype: str
        """
        return self._spec_replicas_path

    @spec_replicas_path.setter
    def spec_replicas_path(self, spec_replicas_path):
        """Sets the spec_replicas_path of this V1CustomResourceSubresourceScale.

        specReplicasPath defines the JSON path inside of a custom resource that corresponds to Scale `spec.replicas`. Only JSON paths without the array notation are allowed. Must be a JSON Path under `.spec`. If there is no value under the given path in the custom resource, the `/scale` subresource will return an error on GET.  # noqa: E501

        :param spec_replicas_path: The spec_replicas_path of this V1CustomResourceSubresourceScale.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and spec_replicas_path is None:  # noqa: E501
            raise ValueError("Invalid value for `spec_replicas_path`, must not be `None`")  # noqa: E501

        self._spec_replicas_path = spec_replicas_path

    @property
    def status_replicas_path(self):
        """Gets the status_replicas_path of this V1CustomResourceSubresourceScale.  # noqa: E501

        statusReplicasPath defines the JSON path inside of a custom resource that corresponds to Scale `status.replicas`. Only JSON paths without the array notation are allowed. Must be a JSON Path under `.status`. If there is no value under the given path in the custom resource, the `status.replicas` value in the `/scale` subresource will default to 0.  # noqa: E501

        :return: The status_replicas_path of this V1CustomResourceSubresourceScale.  # noqa: E501
        :rtype: str
        """
        return self._status_replicas_path

    @status_replicas_path.setter
    def status_replicas_path(self, status_replicas_path):
        """Sets the status_replicas_path of this V1CustomResourceSubresourceScale.

        statusReplicasPath defines the JSON path inside of a custom resource that corresponds to Scale `status.replicas`. Only JSON paths without the array notation are allowed. Must be a JSON Path under `.status`. If there is no value under the given path in the custom resource, the `status.replicas` value in the `/scale` subresource will default to 0.  # noqa: E501

        :param status_replicas_path: The status_replicas_path of this V1CustomResourceSubresourceScale.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and status_replicas_path is None:  # noqa: E501
            raise ValueError("Invalid value for `status_replicas_path`, must not be `None`")  # noqa: E501

        self._status_replicas_path = status_replicas_path

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1CustomResourceSubresourceScale):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1CustomResourceSubresourceScale):
            return True

        return self.to_dict() != other.to_dict()
