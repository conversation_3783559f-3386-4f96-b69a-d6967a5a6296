# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1EndpointHints(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'for_nodes': 'list[V1ForNode]',
        'for_zones': 'list[V1ForZone]'
    }

    attribute_map = {
        'for_nodes': 'forNodes',
        'for_zones': 'forZones'
    }

    def __init__(self, for_nodes=None, for_zones=None, local_vars_configuration=None):  # noqa: E501
        """V1EndpointHints - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._for_nodes = None
        self._for_zones = None
        self.discriminator = None

        if for_nodes is not None:
            self.for_nodes = for_nodes
        if for_zones is not None:
            self.for_zones = for_zones

    @property
    def for_nodes(self):
        """Gets the for_nodes of this V1EndpointHints.  # noqa: E501

        forNodes indicates the node(s) this endpoint should be consumed by when using topology aware routing. May contain a maximum of 8 entries. This is an Alpha feature and is only used when the PreferSameTrafficDistribution feature gate is enabled.  # noqa: E501

        :return: The for_nodes of this V1EndpointHints.  # noqa: E501
        :rtype: list[V1ForNode]
        """
        return self._for_nodes

    @for_nodes.setter
    def for_nodes(self, for_nodes):
        """Sets the for_nodes of this V1EndpointHints.

        forNodes indicates the node(s) this endpoint should be consumed by when using topology aware routing. May contain a maximum of 8 entries. This is an Alpha feature and is only used when the PreferSameTrafficDistribution feature gate is enabled.  # noqa: E501

        :param for_nodes: The for_nodes of this V1EndpointHints.  # noqa: E501
        :type: list[V1ForNode]
        """

        self._for_nodes = for_nodes

    @property
    def for_zones(self):
        """Gets the for_zones of this V1EndpointHints.  # noqa: E501

        forZones indicates the zone(s) this endpoint should be consumed by when using topology aware routing. May contain a maximum of 8 entries.  # noqa: E501

        :return: The for_zones of this V1EndpointHints.  # noqa: E501
        :rtype: list[V1ForZone]
        """
        return self._for_zones

    @for_zones.setter
    def for_zones(self, for_zones):
        """Sets the for_zones of this V1EndpointHints.

        forZones indicates the zone(s) this endpoint should be consumed by when using topology aware routing. May contain a maximum of 8 entries.  # noqa: E501

        :param for_zones: The for_zones of this V1EndpointHints.  # noqa: E501
        :type: list[V1ForZone]
        """

        self._for_zones = for_zones

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1EndpointHints):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1EndpointHints):
            return True

        return self.to_dict() != other.to_dict()
