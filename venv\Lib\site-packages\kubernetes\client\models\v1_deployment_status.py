# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1DeploymentStatus(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'available_replicas': 'int',
        'collision_count': 'int',
        'conditions': 'list[V1DeploymentCondition]',
        'observed_generation': 'int',
        'ready_replicas': 'int',
        'replicas': 'int',
        'terminating_replicas': 'int',
        'unavailable_replicas': 'int',
        'updated_replicas': 'int'
    }

    attribute_map = {
        'available_replicas': 'availableReplicas',
        'collision_count': 'collisionCount',
        'conditions': 'conditions',
        'observed_generation': 'observedGeneration',
        'ready_replicas': 'readyReplicas',
        'replicas': 'replicas',
        'terminating_replicas': 'terminatingReplicas',
        'unavailable_replicas': 'unavailableReplicas',
        'updated_replicas': 'updatedReplicas'
    }

    def __init__(self, available_replicas=None, collision_count=None, conditions=None, observed_generation=None, ready_replicas=None, replicas=None, terminating_replicas=None, unavailable_replicas=None, updated_replicas=None, local_vars_configuration=None):  # noqa: E501
        """V1DeploymentStatus - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._available_replicas = None
        self._collision_count = None
        self._conditions = None
        self._observed_generation = None
        self._ready_replicas = None
        self._replicas = None
        self._terminating_replicas = None
        self._unavailable_replicas = None
        self._updated_replicas = None
        self.discriminator = None

        if available_replicas is not None:
            self.available_replicas = available_replicas
        if collision_count is not None:
            self.collision_count = collision_count
        if conditions is not None:
            self.conditions = conditions
        if observed_generation is not None:
            self.observed_generation = observed_generation
        if ready_replicas is not None:
            self.ready_replicas = ready_replicas
        if replicas is not None:
            self.replicas = replicas
        if terminating_replicas is not None:
            self.terminating_replicas = terminating_replicas
        if unavailable_replicas is not None:
            self.unavailable_replicas = unavailable_replicas
        if updated_replicas is not None:
            self.updated_replicas = updated_replicas

    @property
    def available_replicas(self):
        """Gets the available_replicas of this V1DeploymentStatus.  # noqa: E501

        Total number of available non-terminating pods (ready for at least minReadySeconds) targeted by this deployment.  # noqa: E501

        :return: The available_replicas of this V1DeploymentStatus.  # noqa: E501
        :rtype: int
        """
        return self._available_replicas

    @available_replicas.setter
    def available_replicas(self, available_replicas):
        """Sets the available_replicas of this V1DeploymentStatus.

        Total number of available non-terminating pods (ready for at least minReadySeconds) targeted by this deployment.  # noqa: E501

        :param available_replicas: The available_replicas of this V1DeploymentStatus.  # noqa: E501
        :type: int
        """

        self._available_replicas = available_replicas

    @property
    def collision_count(self):
        """Gets the collision_count of this V1DeploymentStatus.  # noqa: E501

        Count of hash collisions for the Deployment. The Deployment controller uses this field as a collision avoidance mechanism when it needs to create the name for the newest ReplicaSet.  # noqa: E501

        :return: The collision_count of this V1DeploymentStatus.  # noqa: E501
        :rtype: int
        """
        return self._collision_count

    @collision_count.setter
    def collision_count(self, collision_count):
        """Sets the collision_count of this V1DeploymentStatus.

        Count of hash collisions for the Deployment. The Deployment controller uses this field as a collision avoidance mechanism when it needs to create the name for the newest ReplicaSet.  # noqa: E501

        :param collision_count: The collision_count of this V1DeploymentStatus.  # noqa: E501
        :type: int
        """

        self._collision_count = collision_count

    @property
    def conditions(self):
        """Gets the conditions of this V1DeploymentStatus.  # noqa: E501

        Represents the latest available observations of a deployment's current state.  # noqa: E501

        :return: The conditions of this V1DeploymentStatus.  # noqa: E501
        :rtype: list[V1DeploymentCondition]
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this V1DeploymentStatus.

        Represents the latest available observations of a deployment's current state.  # noqa: E501

        :param conditions: The conditions of this V1DeploymentStatus.  # noqa: E501
        :type: list[V1DeploymentCondition]
        """

        self._conditions = conditions

    @property
    def observed_generation(self):
        """Gets the observed_generation of this V1DeploymentStatus.  # noqa: E501

        The generation observed by the deployment controller.  # noqa: E501

        :return: The observed_generation of this V1DeploymentStatus.  # noqa: E501
        :rtype: int
        """
        return self._observed_generation

    @observed_generation.setter
    def observed_generation(self, observed_generation):
        """Sets the observed_generation of this V1DeploymentStatus.

        The generation observed by the deployment controller.  # noqa: E501

        :param observed_generation: The observed_generation of this V1DeploymentStatus.  # noqa: E501
        :type: int
        """

        self._observed_generation = observed_generation

    @property
    def ready_replicas(self):
        """Gets the ready_replicas of this V1DeploymentStatus.  # noqa: E501

        Total number of non-terminating pods targeted by this Deployment with a Ready Condition.  # noqa: E501

        :return: The ready_replicas of this V1DeploymentStatus.  # noqa: E501
        :rtype: int
        """
        return self._ready_replicas

    @ready_replicas.setter
    def ready_replicas(self, ready_replicas):
        """Sets the ready_replicas of this V1DeploymentStatus.

        Total number of non-terminating pods targeted by this Deployment with a Ready Condition.  # noqa: E501

        :param ready_replicas: The ready_replicas of this V1DeploymentStatus.  # noqa: E501
        :type: int
        """

        self._ready_replicas = ready_replicas

    @property
    def replicas(self):
        """Gets the replicas of this V1DeploymentStatus.  # noqa: E501

        Total number of non-terminating pods targeted by this deployment (their labels match the selector).  # noqa: E501

        :return: The replicas of this V1DeploymentStatus.  # noqa: E501
        :rtype: int
        """
        return self._replicas

    @replicas.setter
    def replicas(self, replicas):
        """Sets the replicas of this V1DeploymentStatus.

        Total number of non-terminating pods targeted by this deployment (their labels match the selector).  # noqa: E501

        :param replicas: The replicas of this V1DeploymentStatus.  # noqa: E501
        :type: int
        """

        self._replicas = replicas

    @property
    def terminating_replicas(self):
        """Gets the terminating_replicas of this V1DeploymentStatus.  # noqa: E501

        Total number of terminating pods targeted by this deployment. Terminating pods have a non-null .metadata.deletionTimestamp and have not yet reached the Failed or Succeeded .status.phase.  This is an alpha field. Enable DeploymentReplicaSetTerminatingReplicas to be able to use this field.  # noqa: E501

        :return: The terminating_replicas of this V1DeploymentStatus.  # noqa: E501
        :rtype: int
        """
        return self._terminating_replicas

    @terminating_replicas.setter
    def terminating_replicas(self, terminating_replicas):
        """Sets the terminating_replicas of this V1DeploymentStatus.

        Total number of terminating pods targeted by this deployment. Terminating pods have a non-null .metadata.deletionTimestamp and have not yet reached the Failed or Succeeded .status.phase.  This is an alpha field. Enable DeploymentReplicaSetTerminatingReplicas to be able to use this field.  # noqa: E501

        :param terminating_replicas: The terminating_replicas of this V1DeploymentStatus.  # noqa: E501
        :type: int
        """

        self._terminating_replicas = terminating_replicas

    @property
    def unavailable_replicas(self):
        """Gets the unavailable_replicas of this V1DeploymentStatus.  # noqa: E501

        Total number of unavailable pods targeted by this deployment. This is the total number of pods that are still required for the deployment to have 100% available capacity. They may either be pods that are running but not yet available or pods that still have not been created.  # noqa: E501

        :return: The unavailable_replicas of this V1DeploymentStatus.  # noqa: E501
        :rtype: int
        """
        return self._unavailable_replicas

    @unavailable_replicas.setter
    def unavailable_replicas(self, unavailable_replicas):
        """Sets the unavailable_replicas of this V1DeploymentStatus.

        Total number of unavailable pods targeted by this deployment. This is the total number of pods that are still required for the deployment to have 100% available capacity. They may either be pods that are running but not yet available or pods that still have not been created.  # noqa: E501

        :param unavailable_replicas: The unavailable_replicas of this V1DeploymentStatus.  # noqa: E501
        :type: int
        """

        self._unavailable_replicas = unavailable_replicas

    @property
    def updated_replicas(self):
        """Gets the updated_replicas of this V1DeploymentStatus.  # noqa: E501

        Total number of non-terminating pods targeted by this deployment that have the desired template spec.  # noqa: E501

        :return: The updated_replicas of this V1DeploymentStatus.  # noqa: E501
        :rtype: int
        """
        return self._updated_replicas

    @updated_replicas.setter
    def updated_replicas(self, updated_replicas):
        """Sets the updated_replicas of this V1DeploymentStatus.

        Total number of non-terminating pods targeted by this deployment that have the desired template spec.  # noqa: E501

        :param updated_replicas: The updated_replicas of this V1DeploymentStatus.  # noqa: E501
        :type: int
        """

        self._updated_replicas = updated_replicas

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1DeploymentStatus):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1DeploymentStatus):
            return True

        return self.to_dict() != other.to_dict()
