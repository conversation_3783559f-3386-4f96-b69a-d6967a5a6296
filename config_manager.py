# config_manager.py
# -*- coding: utf-8 -*-
import json
import os
import threading
from typing import List
from llm_adapters import create_llm_adapter
from embedding_adapters import create_embedding_adapter


def get_available_models(interface_format: str, api_key: str, base_url: str) -> List[str]:
    """获取可用的模型列表"""
    try:
        if interface_format == "openai":
            import openai
            client = openai.OpenAI(api_key=api_key, base_url=base_url)
            models = client.models.list()
            return [model.id for model in models.data]

        elif interface_format == "gemini":
            import google.generativeai as genai
            genai.configure(api_key=api_key)
            # Gemini常用模型列表
            return [
                "gemini-2.5-pro", "gemini-2.5-flash", "gemini-2.0-flash",
                "gemini-2.0-flash-lite", "gemini-pro", "gemini-pro-vision"
            ]

        elif interface_format == "azure":
            # Azure模型需要从部署中获取，这里返回常用模型
            return ["gpt-4", "gpt-4-turbo", "gpt-35-turbo", "gpt-4o", "gpt-4o-mini"]

        elif interface_format == "ollama":
            import requests
            try:
                response = requests.get(f"{base_url}/api/tags", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    return [model["name"] for model in data.get("models", [])]
            except:
                pass
            # 返回常用本地模型
            return ["llama2", "codellama", "mistral", "qwen", "chatglm"]

        else:
            # 默认返回常用模型
            return ["gpt-4", "gpt-3.5-turbo", "gemini-pro", "claude-3-sonnet"]

    except Exception as e:
        print(f"获取模型列表失败: {e}")
        # 返回默认模型列表
        return ["gpt-4", "gpt-3.5-turbo", "gemini-pro", "claude-3-sonnet"]

def load_config(config_file: str) -> dict:
    """从指定的 config_file 加载配置，若不存在则返回空字典。"""
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            pass
    return {}

def save_config(config_data: dict, config_file: str) -> bool:
    """将 config_data 保存到 config_file 中，返回 True/False 表示是否成功。"""
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=4)
        return True
    except:
        return False

def test_llm_config(interface_format, api_key, base_url, model_name, temperature, max_tokens, timeout, log_func, handle_exception_func):
    """测试当前的LLM配置是否可用"""
    def task():
        try:
            log_func("开始测试LLM配置...")
            llm_adapter = create_llm_adapter(
                interface_format=interface_format,
                base_url=base_url,
                model_name=model_name,
                api_key=api_key,
                temperature=temperature,
                max_tokens=max_tokens,
                timeout=timeout
            )

            test_prompt = "Please reply 'OK'"
            response = llm_adapter.invoke(test_prompt)
            if response:
                log_func("✅ LLM配置测试成功！")
                log_func(f"测试回复: {response}")
            else:
                log_func("❌ LLM配置测试失败：未获取到响应")
        except Exception as e:
            log_func(f"❌ LLM配置测试出错: {str(e)}")
            handle_exception_func("测试LLM配置时出错")

    threading.Thread(target=task, daemon=True).start()

def test_embedding_config(api_key, base_url, interface_format, model_name, log_func, handle_exception_func):
    """测试当前的Embedding配置是否可用"""
    def task():
        try:
            log_func("开始测试Embedding配置...")
            embedding_adapter = create_embedding_adapter(
                interface_format=interface_format,
                api_key=api_key,
                base_url=base_url,
                model_name=model_name
            )

            test_text = "测试文本"
            embeddings = embedding_adapter.embed_query(test_text)
            if embeddings and len(embeddings) > 0:
                log_func("✅ Embedding配置测试成功！")
                log_func(f"生成的向量维度: {len(embeddings)}")
            else:
                log_func("❌ Embedding配置测试失败：未获取到向量")
        except Exception as e:
            log_func(f"❌ Embedding配置测试出错: {str(e)}")
            handle_exception_func("测试Embedding配置时出错")

    threading.Thread(target=task, daemon=True).start()

def get_default_polish_config() -> dict:
    """获取默认的润色配置"""
    return {
        "default_polish_level": "medium",  # light, medium, deep
        "auto_backup": True,  # 是否自动备份原文件
        "show_ai_score": True,  # 是否显示AI味评分
        "batch_delay": 2,  # 批量处理时的延迟（秒）
        "quality_threshold": 0.1,  # 质量改善阈值
        "style_reference_dir": "",  # 风格参考文件目录
        "polish_output_dir": "",  # 润色输出目录
        "enable_quality_assessment": True,  # 是否启用质量评估
        "save_analysis_report": True,  # 是否保存分析报告
        "max_content_length": 5000,  # 单次润色最大字符数

        # 润色专用模型配置
        "use_dedicated_model": False,  # 是否使用专用润色模型
        "polish_interface_format": "",  # 润色专用接口格式
        "polish_api_key": "",  # 润色专用API密钥
        "polish_base_url": "",  # 润色专用基础URL
        "polish_model_name": "",  # 润色专用模型名称
        "polish_temperature": 0.7,  # 润色专用温度
        "polish_max_tokens": 4000,  # 润色专用最大令牌数
        "polish_timeout": 120,  # 润色专用超时时间
    }

def load_polish_config(config_file: str) -> dict:
    """加载润色配置"""
    config = load_config(config_file)
    polish_config = config.get("polish_config", {})

    # 合并默认配置
    default_config = get_default_polish_config()
    for key, value in default_config.items():
        if key not in polish_config:
            polish_config[key] = value

    return polish_config

def save_polish_config(config_file: str, polish_config: dict) -> bool:
    """保存润色配置"""
    try:
        # 加载现有配置
        config = load_config(config_file)

        # 更新润色配置
        config["polish_config"] = polish_config

        # 保存配置
        return save_config(config, config_file)
    except Exception as e:
        print(f"保存润色配置失败: {e}")
        return False

def test_polish_config(
    interface_format: str,
    api_key: str,
    base_url: str,
    model_name: str,
    temperature: float,
    max_tokens: int,
    timeout: int,
    log_func,
    handle_exception_func
):
    """测试润色配置是否可用"""
    def task():
        try:
            log_func("开始测试润色配置...")

            # 导入润色模块
            from novel_generator.polish import polish_text_content, PolishLevel

            # 测试文本
            test_content = """突然，他心中涌起了一阵不安。然而，与此同时，他不禁想到了那个神秘的女子。
她的眼中闪过一丝复杂的情感，仿佛看到了什么不可思议的事情。这一刻，他深深地感受到了命运的安排。
的确，这一切都太过巧合了。"""

            # 执行测试润色
            polished_content, analysis_report = polish_text_content(
                interface_format=interface_format,
                api_key=api_key,
                base_url=base_url,
                llm_model=model_name,
                content=test_content,
                polish_level=PolishLevel.LIGHT,
                temperature=temperature,
                max_tokens=max_tokens,
                timeout=timeout
            )

            if polished_content and polished_content != test_content:
                log_func("✅ 润色配置测试成功！")
                log_func(f"原文AI味评分: {analysis_report.get('original_ai_score', 0):.3f}")
                log_func(f"润色后AI味评分: {analysis_report.get('polished_ai_score', 0):.3f}")
                log_func(f"改善程度: {analysis_report.get('improvement', 0):.3f}")
                log_func(f"润色后内容预览: {polished_content[:100]}...")
            else:
                log_func("❌ 润色配置测试失败：未获取到有效的润色结果")

        except Exception as e:
            log_func(f"❌ 润色配置测试出错: {str(e)}")
            handle_exception_func("测试润色配置时出错")

    threading.Thread(target=task, daemon=True).start()

def validate_polish_config(polish_config: dict) -> tuple[bool, str]:
    """验证润色配置的有效性"""
    try:
        # 检查必要的配置项
        required_keys = ["default_polish_level", "auto_backup", "show_ai_score"]
        for key in required_keys:
            if key not in polish_config:
                return False, f"缺少必要的配置项: {key}"

        # 检查润色级别
        valid_levels = ["light", "medium", "deep"]
        if polish_config["default_polish_level"] not in valid_levels:
            return False, f"无效的润色级别: {polish_config['default_polish_level']}"

        # 检查数值范围
        if polish_config.get("batch_delay", 0) < 0:
            return False, "批量延迟时间不能为负数"

        if polish_config.get("quality_threshold", 0) < 0 or polish_config.get("quality_threshold", 0) > 1:
            return False, "质量阈值必须在0-1之间"

        if polish_config.get("max_content_length", 0) <= 0:
            return False, "最大内容长度必须大于0"

        # 检查目录路径
        style_dir = polish_config.get("style_reference_dir", "")
        if style_dir and not os.path.exists(style_dir):
            return False, f"风格参考目录不存在: {style_dir}"

        output_dir = polish_config.get("polish_output_dir", "")
        if output_dir and not os.path.exists(output_dir):
            return False, f"润色输出目录不存在: {output_dir}"

        return True, "配置验证通过"

    except Exception as e:
        return False, f"配置验证出错: {str(e)}"