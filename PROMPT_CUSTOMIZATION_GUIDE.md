# 润色提示词自定义指南

## 🎯 概述

AI小说生成器现在支持自定义润色提示词，您可以通过编辑文本文件来调整润色策略，无需修改代码。

## 📁 文件位置

提示词文件位于项目根目录的 `prompts/` 文件夹中：

```
prompts/
├── README.md           # 详细说明文档
├── polish_light.txt    # 轻度润色提示词
├── polish_medium.txt   # 中度润色提示词
└── polish_deep.txt     # 深度润色提示词
```

## 🔧 快速开始

### 1. 编辑提示词文件

使用任何文本编辑器打开对应的提示词文件，例如：
- 记事本
- VS Code
- Notepad++

### 2. 修改提示词内容

在文件中编写您的自定义提示词，使用占位符来插入动态内容：

```
你是一位专业的文学编辑，请润色以下文本：

【原文】
{content}

【要求】
- 保持原文意思不变
- 语言更加自然流畅
- {user_guidance}

请直接输出润色后的文本：
```

### 3. 保存并测试

保存文件后，下次使用润色功能时会自动应用新的提示词。

## 📝 占位符参考

| 占位符 | 说明 | 示例 |
|--------|------|------|
| `{content}` | 待润色的原文内容 | 必须包含 |
| `{character_info}` | 角色信息 | "主角：张三，配角：李四" |
| `{scene_context}` | 场景上下文 | "古代宫廷，紧张氛围" |
| `{style_reference}` | 风格参考 | "金庸武侠风格" |
| `{user_guidance}` | 用户指导 | "保持对话简洁" |
| `{ai_score:.2f}` | AI味评分 | "0.75" |
| `{ai_traits_text}` | AI特征分析 | "检测到重复模式" |

## ⚠️ 重要提示

### 必须遵守的规则

1. **文件编码**: 必须使用 UTF-8 编码保存
2. **占位符语法**: 确保占位符拼写正确
3. **输出要求**: 明确要求只输出润色后的文本
4. **格式保持**: 要求保持原文格式和段落结构

### 推荐的提示词结构

```
[角色设定] - 定义AI的身份
[任务描述] - 说明润色目标
[原文内容] - {content} 占位符
[参考信息] - 其他占位符
[输出要求] - 明确输出格式
```

## 🎨 自定义示例

### 示例1：简洁风格

```
作为简洁文风的编辑，请润色以下文本：

原文：{content}

要求：
- 删除冗余词汇
- 简化句式结构
- {user_guidance}

直接输出润色结果：
```

### 示例2：文学风格

```
你是一位文学大师，请将以下文本润色得更有文学性：

【待润色文本】
{content}

【风格要求】
- 语言优美典雅
- 意境深远
- 参考风格：{style_reference}

【特殊指导】
{user_guidance}

请输出润色后的文学作品：
```

## 🔄 错误处理

如果提示词文件出现问题，系统会：

1. 在控制台显示警告信息
2. 自动使用内置的默认提示词
3. 确保润色功能正常工作

常见问题：
- 文件不存在 → 使用默认提示词
- 文件为空 → 使用默认提示词
- 编码错误 → 使用默认提示词
- 占位符错误 → 可能导致格式化失败

## 💡 优化技巧

### 1. 明确指令

```
❌ 请润色文本
✅ 请直接输出润色后的文本，不要添加任何解释
```

### 2. 保持格式

```
❌ 润色后输出
✅ 保持原文的段落结构和格式，直接输出润色内容
```

### 3. 避免解释

```
❌ 请润色并说明修改原因
✅ 只输出润色后的文本内容，不要任何说明
```

## 🚀 高级技巧

### 条件性指令

```
{content}

{user_guidance}

根据以上要求进行润色，如果用户没有特殊要求，则按标准流程处理。
```

### 多层次要求

```
【基础要求】
- 语言自然流畅
- 保持原意不变

【进阶要求】
- 根据角色信息调整语言风格：{character_info}
- 结合场景氛围：{scene_context}

【输出标准】
直接输出润色文本，确保格式完整。
```

## 📞 技术支持

如果在使用过程中遇到问题：

1. 检查文件编码是否为 UTF-8
2. 验证占位符语法是否正确
3. 查看控制台是否有警告信息
4. 尝试恢复默认提示词进行对比

---

通过自定义提示词，您可以打造专属的润色风格，让AI更好地理解和执行您的润色需求！
