# Gemini API 配置指南

## 🎯 问题解决

**原始问题**：`AttributeError: module 'google.generativeai' has no attribute 'Client'`

**解决状态**：✅ **已完全修复**

## 🔧 修复内容

### 1. API调用方式更新
- ❌ 旧版本：`genai.Client()`（已废弃）
- ✅ 新版本：`genai.configure()` + `genai.GenerativeModel()`

### 2. 多重容错机制
- 支持不同版本的`google-generativeai`库
- 自动尝试多种调用方式
- 智能降级处理

### 3. 响应解析优化
- 支持不同的响应格式
- 自动从`candidates`中提取文本
- 完善的错误处理

## 📋 配置步骤

### 1. 获取Gemini API密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录Google账号
3. 点击"Create API Key"
4. 复制生成的API密钥

### 2. 在程序中配置

1. **打开AI小说生成器**
2. **进入配置页面**
3. **选择接口格式**：选择"Gemini"
4. **填入API密钥**：粘贴从Google AI Studio获取的密钥
5. **选择模型**：
   - `gemini-2.5-pro` - 最新高性能模型
   - `gemini-2.5-flash` - 快速响应模型
   - `gemini-2.0-flash` - 平衡性能模型
   - `gemini-pro` - 经典模型
6. **Base URL**：留空（程序会自动使用正确的端点）

### 3. 测试配置

1. 点击"测试配置"按钮
2. 如果看到"API key not valid"错误，请检查：
   - API密钥是否正确复制
   - API密钥是否已激活
   - 网络连接是否正常

## ⚠️ 常见问题

### Q1: 仍然出现"has no attribute 'Client'"错误
**A**: 请重启程序，确保使用了最新的修复代码。

### Q2: "API key not valid"错误
**A**: 
- 检查API密钥是否正确
- 确认在Google AI Studio中已启用API
- 检查网络连接

### Q3: 模型列表为空
**A**: 
- 确保API密钥有效
- 检查网络连接
- 尝试手动输入模型名称

### Q4: 响应为空
**A**: 
- 检查模型名称是否正确
- 确认API配额是否充足
- 尝试降低max_tokens设置

## 🚀 支持的模型

| 模型名称 | 特点 | 推荐用途 |
|---------|------|---------|
| `gemini-2.5-pro` | 最新高性能 | 复杂创作任务 |
| `gemini-2.5-flash` | 快速响应 | 实时润色 |
| `gemini-2.0-flash` | 平衡性能 | 日常使用 |
| `gemini-pro` | 经典稳定 | 兼容性优先 |

## 📞 技术支持

如果仍然遇到问题：

1. **检查错误日志**：查看控制台输出的详细错误信息
2. **验证网络**：确保能访问Google服务
3. **更新库版本**：确保`google-generativeai`库是最新版本

---

**修复状态**：✅ 完成  
**测试状态**：✅ 通过  
**兼容性**：✅ 支持多版本google-generativeai库
