# ui/polish_tab.py
# -*- coding: utf-8 -*-
"""
润色模块UI - 为AI小说生成器提供文本润色功能
删除了专用模型配置功能，直接使用主功能的模型配置
"""
import os
import threading
import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk
from novel_generator.polish import (
    polish_text_content,
    batch_polish_chapters,
    PolishLevel,
    AITraitDetector
)

def build_polish_config_area(self):
    """构建润色配置区域"""
    config_frame = ctk.CTkFrame(self.polish_main_frame)
    config_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
    config_frame.grid_columnconfigure(1, weight=1)

    # 第一行配置 - 润色级别
    ctk.CTkLabel(config_frame, text="润色级别:", font=("Microsoft YaHei", 12)).grid(
        row=0, column=0, padx=5, pady=5, sticky="w"
    )

    self.polish_level_var = ctk.StringVar(value="medium")
    polish_level_menu = ctk.CTkOptionMenu(
        config_frame,
        variable=self.polish_level_var,
        values=["light", "medium", "deep"],
        command=on_polish_level_changed
    )
    polish_level_menu.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

    # 第二行配置 - 风格参考
    ctk.CTkLabel(config_frame, text="风格参考:", font=("Microsoft YaHei", 12)).grid(
        row=1, column=0, padx=5, pady=5, sticky="w"
    )

    self.style_reference_var = ctk.StringVar()
    style_reference_entry = ctk.CTkEntry(
        config_frame,
        textvariable=self.style_reference_var,
        placeholder_text="选择风格参考文件（可选）"
    )
    style_reference_entry.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

    browse_style_btn = ctk.CTkButton(
        config_frame,
        text="浏览",
        width=60,
        command=lambda: browse_style_reference_file(self)
    )
    browse_style_btn.grid(row=1, column=2, padx=5, pady=5)

    # 删除专用润色模型配置功能，直接使用主功能的模型配置

def build_polish_content_area(self):
    """构建内容编辑区域"""
    content_frame = ctk.CTkFrame(self.polish_main_frame)
    content_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)
    content_frame.grid_columnconfigure(0, weight=1)
    content_frame.grid_columnconfigure(1, weight=1)
    content_frame.grid_rowconfigure(1, weight=1)

    # 标题行
    ctk.CTkLabel(content_frame, text="原文内容", font=("Microsoft YaHei", 14, "bold")).grid(
        row=0, column=0, padx=5, pady=5
    )
    ctk.CTkLabel(content_frame, text="润色后内容", font=("Microsoft YaHei", 14, "bold")).grid(
        row=0, column=1, padx=5, pady=5
    )

    # 文本编辑区域
    self.original_text = ctk.CTkTextbox(content_frame, height=300)
    self.original_text.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")

    self.polished_text = ctk.CTkTextbox(content_frame, height=300)
    self.polished_text.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")

def build_polish_control_area(self):
    """构建控制按钮区域"""
    control_frame = ctk.CTkFrame(self.polish_main_frame)
    control_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=5, pady=5)

    # 左侧按钮组
    left_frame = ctk.CTkFrame(control_frame)
    left_frame.pack(side="left", fill="x", expand=True, padx=5, pady=5)

    ctk.CTkButton(
        left_frame,
        text="加载章节",
        command=lambda: load_chapter_for_polish(self)
    ).pack(side="left", padx=5)

    ctk.CTkButton(
        left_frame,
        text="润色文本",
        command=lambda: polish_current_text(self)
    ).pack(side="left", padx=5)

    ctk.CTkButton(
        left_frame,
        text="保存结果",
        command=lambda: save_polished_text(self)
    ).pack(side="left", padx=5)

    # 右侧状态显示
    right_frame = ctk.CTkFrame(control_frame)
    right_frame.pack(side="right", padx=5, pady=5)

    self.polish_status_label = ctk.CTkLabel(
        right_frame,
        text="就绪",
        font=("Microsoft YaHei", 12)
    )
    self.polish_status_label.pack(padx=10, pady=5)

def build_polish_batch_area(self):
    """构建批量处理区域"""
    batch_frame = ctk.CTkFrame(self.polish_main_frame)
    batch_frame.grid(row=3, column=0, columnspan=2, sticky="ew", padx=5, pady=5)

    ctk.CTkLabel(batch_frame, text="批量处理", font=("Microsoft YaHei", 14, "bold")).pack(
        anchor="w", padx=10, pady=5
    )

    batch_control_frame = ctk.CTkFrame(batch_frame)
    batch_control_frame.pack(fill="x", padx=10, pady=5)

    # 输入目录选择
    input_frame = ctk.CTkFrame(batch_control_frame)
    input_frame.pack(fill="x", pady=2)

    ctk.CTkLabel(input_frame, text="输入目录:", width=80).pack(side="left", padx=5)
    self.batch_input_var = ctk.StringVar()
    input_entry = ctk.CTkEntry(input_frame, textvariable=self.batch_input_var)
    input_entry.pack(side="left", fill="x", expand=True, padx=5)
    ctk.CTkButton(
        input_frame,
        text="浏览",
        width=60,
        command=lambda: browse_batch_input_dir(self)
    ).pack(side="right", padx=5)

    # 输出目录选择
    output_frame = ctk.CTkFrame(batch_control_frame)
    output_frame.pack(fill="x", pady=2)

    ctk.CTkLabel(output_frame, text="输出目录:", width=80).pack(side="left", padx=5)
    self.batch_output_var = ctk.StringVar()
    output_entry = ctk.CTkEntry(output_frame, textvariable=self.batch_output_var)
    output_entry.pack(side="left", fill="x", expand=True, padx=5)
    ctk.CTkButton(
        output_frame,
        text="浏览",
        width=60,
        command=lambda: browse_batch_output_dir(self)
    ).pack(side="right", padx=5)

    # 批量处理按钮
    batch_btn_frame = ctk.CTkFrame(batch_control_frame)
    batch_btn_frame.pack(fill="x", pady=5)

    ctk.CTkButton(
        batch_btn_frame,
        text="开始批量润色",
        command=lambda: start_batch_polish(self)
    ).pack(side="left", padx=5)

    # 批量处理状态
    self.batch_status_label = ctk.CTkLabel(
        batch_btn_frame,
        text="未分析",
        font=("Microsoft YaHei", 10)
    )
    self.batch_status_label.pack(side="left", padx=10)

def build_polish_tab(self):
    """构建润色标签页"""
    # 创建主框架
    self.polish_main_frame = ctk.CTkFrame(self.polish_tab)
    self.polish_main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    self.polish_main_frame.grid_columnconfigure(0, weight=1)
    self.polish_main_frame.grid_columnconfigure(1, weight=1)
    self.polish_main_frame.grid_rowconfigure(1, weight=1)

    # 构建各个区域
    build_polish_config_area(self)
    build_polish_content_area(self)
    build_polish_control_area(self)
    build_polish_batch_area(self)

    # 绑定方法到实例
    self.update_polish_status = update_polish_status

    # 确保状态标签存在
    if not hasattr(self, 'polish_status_label'):
        self.polish_status_label = ctk.CTkLabel(self.polish_main_frame, text="就绪")
        self.polish_status_label.grid(row=4, column=0, columnspan=2, pady=5)

# 事件处理函数
def on_polish_level_changed(value):
    """润色级别改变时的回调"""
    level_descriptions = {
        PolishLevel.LIGHT: "轻度润色：主要修正语言表达问题",
        PolishLevel.MEDIUM: "中度润色：改善表达并增加细节",
        PolishLevel.DEEP: "深度润色：全面重写以达到人类作者水准"
    }
    # 这里可以添加提示信息显示
    pass

def browse_style_reference_file(self):
    """浏览风格参考文件"""
    file_path = filedialog.askopenfilename(
        title="选择风格参考文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
    )
    if file_path:
        self.style_reference_var.set(file_path)

def load_chapter_for_polish(self):
    """加载章节文件进行润色"""
    file_path = filedialog.askopenfilename(
        title="选择要润色的章节文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
    )
    if file_path:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            self.original_text.delete("1.0", "end")
            self.original_text.insert("1.0", content)
            self.update_polish_status("章节已加载", "green")
        except Exception as e:
            messagebox.showerror("错误", f"加载文件失败: {str(e)}")

def polish_current_text(self):
    """润色当前文本"""
    content = self.original_text.get("1.0", "end-1c").strip()
    if not content:
        messagebox.showwarning("警告", "请先输入或加载要润色的文本")
        return

    def polish_task():
        try:
            self.update_polish_status("润色中...", "blue")

            # 获取润色级别
            level_map = {
                "light": PolishLevel.LIGHT,
                "medium": PolishLevel.MEDIUM,
                "deep": PolishLevel.DEEP
            }
            polish_level = level_map.get(self.polish_level_var.get(), PolishLevel.MEDIUM)

            # 获取风格参考
            style_reference = None
            style_file = self.style_reference_var.get()
            if style_file and os.path.exists(style_file):
                with open(style_file, 'r', encoding='utf-8') as f:
                    style_reference = f.read()

            # 获取主配置中的模型设置
            from config_manager import load_config
            config = load_config(self.config_file)

            if not config or 'llm_configs' not in config:
                raise Exception("请先在主功能中配置模型")

            # 获取当前选择的接口配置
            current_interface = self.interface_format_var.get()
            interface_mapping = {
                "openai": "OpenAI",
                "deepseek": "DeepSeek",
                "gemini": "Gemini",
                "azure": "Azure",
                "ollama": "Ollama"
            }

            main_interface_key = interface_mapping.get(current_interface, "OpenAI")
            if main_interface_key not in config['llm_configs']:
                raise Exception(f"请先在主功能中配置{main_interface_key}接口")

            llm_config = config['llm_configs'][main_interface_key]

            # 执行润色
            polished_content, _ = polish_text_content(
                interface_format=current_interface,
                api_key=llm_config.get('api_key', ''),
                base_url=llm_config.get('base_url', ''),
                llm_model=llm_config.get('model_name', ''),
                content=content,
                polish_level=polish_level,
                style_reference=style_reference or "",
                temperature=llm_config.get('temperature', 0.7),
                max_tokens=llm_config.get('max_tokens', 4096),
                timeout=llm_config.get('timeout', 600)
            )

            # 更新UI
            self.polished_text.delete("1.0", "end")
            self.polished_text.insert("1.0", polished_content)
            self.update_polish_status("润色完成", "green")

        except Exception as e:
            self.update_polish_status(f"润色失败: {str(e)}", "red")
            messagebox.showerror("错误", f"润色失败: {str(e)}")

    threading.Thread(target=polish_task, daemon=True).start()

def save_polished_text(self):
    """保存润色后的文本"""
    content = self.polished_text.get("1.0", "end-1c").strip()
    if not content:
        messagebox.showwarning("警告", "没有润色后的内容可保存")
        return

    file_path = filedialog.asksaveasfilename(
        title="保存润色后的文本",
        defaultextension=".txt",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
    )
    if file_path:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.update_polish_status("文件已保存", "green")
            messagebox.showinfo("成功", "润色后的文本已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存文件失败: {str(e)}")

def browse_batch_input_dir(self):
    """浏览批量输入目录"""
    dir_path = filedialog.askdirectory(title="选择输入目录")
    if dir_path:
        self.batch_input_var.set(dir_path)

def browse_batch_output_dir(self):
    """浏览批量输出目录"""
    dir_path = filedialog.askdirectory(title="选择输出目录")
    if dir_path:
        self.batch_output_var.set(dir_path)

def start_batch_polish(self):
    """开始批量润色"""
    input_dir = self.batch_input_var.get()
    output_dir = self.batch_output_var.get()

    if not input_dir or not output_dir:
        messagebox.showwarning("警告", "请选择输入和输出目录")
        return

    if not os.path.exists(input_dir):
        messagebox.showerror("错误", "输入目录不存在")
        return

    def batch_task():
        try:
            self.batch_status_label.configure(text="批量润色中...")

            # 获取润色级别
            level_map = {
                "light": PolishLevel.LIGHT,
                "medium": PolishLevel.MEDIUM,
                "deep": PolishLevel.DEEP
            }
            polish_level = level_map.get(self.polish_level_var.get(), PolishLevel.MEDIUM)

            # 获取风格参考
            style_reference = None
            style_file = self.style_reference_var.get()
            if style_file and os.path.exists(style_file):
                with open(style_file, 'r', encoding='utf-8') as f:
                    style_reference = f.read()

            # 获取主配置中的模型设置
            from config_manager import load_config
            config = load_config(self.config_file)

            if not config or 'llm_configs' not in config:
                raise Exception("请先在主功能中配置模型")

            # 获取当前选择的接口配置
            current_interface = self.interface_format_var.get()
            interface_mapping = {
                "openai": "OpenAI",
                "deepseek": "DeepSeek",
                "gemini": "Gemini",
                "azure": "Azure",
                "ollama": "Ollama"
            }

            main_interface_key = interface_mapping.get(current_interface, "OpenAI")
            if main_interface_key not in config['llm_configs']:
                raise Exception(f"请先在主功能中配置{main_interface_key}接口")

            llm_config = config['llm_configs'][main_interface_key]

            # 获取章节文件列表
            txt_files = [f for f in os.listdir(input_dir) if f.endswith('.txt')]
            if not txt_files:
                raise Exception("输入目录中没有找到txt文件")

            # 提取章节号
            chapter_numbers = []
            for filename in txt_files:
                try:
                    # 假设文件名格式为 "第X章.txt" 或 "X.txt"
                    if filename.startswith('第') and filename.endswith('章.txt'):
                        num_str = filename[1:-4]  # 去掉"第"和"章.txt"
                        chapter_numbers.append(int(num_str))
                    elif filename.replace('.txt', '').isdigit():
                        chapter_numbers.append(int(filename.replace('.txt', '')))
                except:
                    continue

            if not chapter_numbers:
                raise Exception("无法从文件名中提取章节号")

            # 执行批量润色
            result = batch_polish_chapters(
                interface_format=current_interface,
                api_key=llm_config.get('api_key', ''),
                base_url=llm_config.get('base_url', ''),
                llm_model=llm_config.get('model_name', ''),
                chapters_dir=input_dir,
                chapter_numbers=chapter_numbers,
                polish_level=polish_level
            )

            # 统计结果
            total = len(result)
            success_count = sum(1 for success in result.values() if success)
            failed_count = total - success_count

            self.batch_status_label.configure(text=f"完成: {success_count}/{total}")
            messagebox.showinfo("完成", f"批量润色完成\n成功: {success_count}\n失败: {failed_count}")

        except Exception as e:
            self.batch_status_label.configure(text="批量润色失败")
            messagebox.showerror("错误", f"批量润色失败: {str(e)}")

    threading.Thread(target=batch_task, daemon=True).start()

def update_polish_status(self, message, color="black"):
    """更新润色状态"""
    self.polish_status_label.configure(text=message, text_color=color)
