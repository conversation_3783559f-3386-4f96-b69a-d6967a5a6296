# ui/polish_tab.py
# -*- coding: utf-8 -*-
"""
润色功能UI界面
提供章节润色的用户界面，包括配置选项、预览功能和批量处理
"""
import os
import threading
import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk
from novel_generator.polish import (
    polish_text_content,
    polish_chapter_file,
    batch_polish_chapters,
    PolishLevel,
    AITraitDetector
)
from ui.auto_model_loader import AutoModelLoader, ModelLoadingStatus
from utils import read_file, save_string_to_txt

def get_polish_model_config(self):
    """
    获取润色模型配置
    专用润色模型配置优先级高于主配置

    Returns:
        tuple: (interface_format, api_key, base_url, llm_model, temperature, max_tokens, timeout)
    """
    try:
        # 检查是否启用专用润色模型
        if hasattr(self, 'use_dedicated_model_var') and self.use_dedicated_model_var.get():
            # 使用专用润色模型配置
            from config_manager import load_polish_config
            polish_config = load_polish_config("config.json")

            interface_format = polish_config.get("polish_interface_format", self.interface_format_var.get())
            api_key = polish_config.get("polish_api_key", self.api_key_var.get())
            base_url = polish_config.get("polish_base_url", self.base_url_var.get())
            llm_model = polish_config.get("polish_model_name", self.polish_model_var.get())
            temperature = polish_config.get("polish_temperature", self.temperature_var.get())
            max_tokens = polish_config.get("polish_max_tokens", self.max_tokens_var.get())
            timeout = polish_config.get("polish_timeout", self.timeout_var.get())

            return interface_format, api_key, base_url, llm_model, temperature, max_tokens, timeout
        else:
            # 使用主配置模型
            return (
                self.interface_format_var.get(),
                self.api_key_var.get(),
                self.base_url_var.get(),
                self.model_name_var.get(),
                self.temperature_var.get(),
                self.max_tokens_var.get(),
                self.timeout_var.get()
            )
    except Exception as e:
        print(f"获取润色模型配置失败: {e}")
        # 回退到主配置
        return (
            self.interface_format_var.get(),
            self.api_key_var.get(),
            self.base_url_var.get(),
            self.model_name_var.get(),
            self.temperature_var.get(),
            self.max_tokens_var.get(),
            self.timeout_var.get()
        )

def build_polish_tab(self):
    """构建润色功能标签页"""
    # 创建主框架
    self.polish_main_frame = ctk.CTkFrame(self.polish_tab)
    self.polish_main_frame.pack(fill="both", expand=True, padx=10, pady=10)

    # 配置网格
    self.polish_main_frame.grid_columnconfigure(0, weight=1)
    self.polish_main_frame.grid_columnconfigure(1, weight=1)
    self.polish_main_frame.grid_rowconfigure(1, weight=1)

    # 顶部配置区域
    build_polish_config_area(self)

    # 中间内容区域
    build_polish_content_area(self)

    # 底部操作区域
    build_polish_action_area(self)

    # 绑定润色相关方法
    bind_polish_methods(self)

def build_polish_config_area(self):
    """构建润色配置区域"""
    config_frame = ctk.CTkFrame(self.polish_main_frame)
    config_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
    config_frame.grid_columnconfigure(1, weight=1)
    config_frame.grid_columnconfigure(3, weight=1)

    # 润色级别选择
    ctk.CTkLabel(config_frame, text="润色级别:", font=("Microsoft YaHei", 12)).grid(
        row=0, column=0, padx=5, pady=5, sticky="w"
    )

    self.polish_level_var = ctk.StringVar(value=PolishLevel.MEDIUM)
    polish_level_menu = ctk.CTkOptionMenu(
        config_frame,
        variable=self.polish_level_var,
        values=[PolishLevel.LIGHT, PolishLevel.MEDIUM, PolishLevel.DEEP],
        command=on_polish_level_changed
    )
    polish_level_menu.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

    # 风格参考文件
    ctk.CTkLabel(config_frame, text="风格参考:", font=("Microsoft YaHei", 12)).grid(
        row=0, column=2, padx=5, pady=5, sticky="w"
    )

    style_frame = ctk.CTkFrame(config_frame)
    style_frame.grid(row=0, column=3, padx=5, pady=5, sticky="ew")
    style_frame.grid_columnconfigure(0, weight=1)

    self.style_reference_var = ctk.StringVar()
    self.style_reference_entry = ctk.CTkEntry(
        style_frame,
        textvariable=self.style_reference_var,
        placeholder_text="选择风格参考文件..."
    )
    self.style_reference_entry.grid(row=0, column=0, padx=2, pady=2, sticky="ew")

    ctk.CTkButton(
        style_frame,
        text="浏览",
        width=60,
        command=lambda: browse_style_reference_file(self)
    ).grid(row=0, column=1, padx=2, pady=2)

    # 第二行配置
    # 用户指导
    ctk.CTkLabel(config_frame, text="用户指导:", font=("Microsoft YaHei", 12)).grid(
        row=1, column=0, padx=5, pady=5, sticky="w"
    )

    self.user_guidance_entry = ctk.CTkEntry(
        config_frame,
        placeholder_text="输入润色指导要求..."
    )
    self.user_guidance_entry.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

    # 场景上下文
    ctk.CTkLabel(config_frame, text="场景上下文:", font=("Microsoft YaHei", 12)).grid(
        row=1, column=2, padx=5, pady=5, sticky="w"
    )

    self.scene_context_entry = ctk.CTkEntry(
        config_frame,
        placeholder_text="输入场景描述..."
    )
    self.scene_context_entry.grid(row=1, column=3, padx=5, pady=5, sticky="ew")

    # 第三行配置 - 专用模型设置
    # 使用专用模型复选框
    self.use_dedicated_model_var = ctk.BooleanVar()
    self.use_dedicated_model_checkbox = ctk.CTkCheckBox(
        config_frame,
        text="使用专用润色模型",
        variable=self.use_dedicated_model_var,
        command=lambda: toggle_dedicated_model_config(self)
    )
    self.use_dedicated_model_checkbox.grid(row=2, column=0, padx=5, pady=5, sticky="w")

    # 专用模型选择
    ctk.CTkLabel(config_frame, text="润色模型:", font=("Microsoft YaHei", 12)).grid(
        row=2, column=1, padx=5, pady=5, sticky="w"
    )

    self.polish_model_var = ctk.StringVar(value="gpt-4")  # 设置默认值
    self.polish_model_menu = ctk.CTkOptionMenu(
        config_frame,
        variable=self.polish_model_var,
        values=[],
        state="disabled",
        command=on_polish_model_changed
    )
    self.polish_model_menu.grid(row=2, column=2, padx=5, pady=5, sticky="ew")

    # 模型配置按钮
    self.config_polish_model_btn = ctk.CTkButton(
        config_frame,
        text="配置模型",
        width=80,
        command=lambda: configure_polish_model(self),
        state="disabled"
    )
    self.config_polish_model_btn.grid(row=2, column=3, padx=5, pady=5, sticky="w")

    # 加载专用模型配置
    try:
        from config_manager import load_polish_config
        polish_config = load_polish_config("config.json")

        # 设置专用模型选项
        use_dedicated = polish_config.get("use_dedicated_model", False)
        self.use_dedicated_model_var.set(use_dedicated)

        # 加载并设置上次选择的润色模型
        # 优先使用 polish_model, 其次是 polish_model_name
        last_selected_model = polish_config.get("polish_model")
        dedicated_model_name = polish_config.get("polish_model_name")

        if last_selected_model:
            self.polish_model_var.set(last_selected_model)
        elif dedicated_model_name:
            self.polish_model_var.set(dedicated_model_name)
        # 如果两者都为空，则使用 self.polish_model_var 创建时设置的默认值 "gpt-4"

        # 更新控件状态（延迟执行，避免方法未绑定错误）
        # toggle_dedicated_model_config(self)

    except Exception as e:
        print(f"加载润色模型配置失败: {e}")

def build_polish_content_area(self):
    """构建内容编辑区域"""
    content_frame = ctk.CTkFrame(self.polish_main_frame)
    content_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)
    content_frame.grid_columnconfigure(0, weight=1)
    content_frame.grid_columnconfigure(1, weight=1)
    content_frame.grid_rowconfigure(1, weight=1)

    # 标题行
    ctk.CTkLabel(content_frame, text="原文内容", font=("Microsoft YaHei", 14, "bold")).grid(
        row=0, column=0, padx=5, pady=5
    )
    ctk.CTkLabel(content_frame, text="润色后内容", font=("Microsoft YaHei", 14, "bold")).grid(
        row=0, column=1, padx=5, pady=5
    )

    # 原文文本框
    self.original_text = ctk.CTkTextbox(
        content_frame,
        font=("Microsoft YaHei", 11),
        wrap="word"
    )
    self.original_text.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")

    # 润色后文本框
    self.polished_text = ctk.CTkTextbox(
        content_frame,
        font=("Microsoft YaHei", 11),
        wrap="word"
    )
    self.polished_text.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")

    # 润色状态显示区域
    status_frame = ctk.CTkFrame(content_frame)
    status_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=5, pady=(5, 2))
    status_frame.grid_columnconfigure(1, weight=1)
    status_frame.grid_columnconfigure(3, weight=1)

    ctk.CTkLabel(status_frame, text="润色状态:", font=("Microsoft YaHei", 12)).grid(
        row=0, column=0, padx=5, pady=5, sticky="w"
    )
    self.polish_status_label = ctk.CTkLabel(
        status_frame,
        text="待执行",
        font=("Microsoft YaHei", 12, "bold"),
        text_color="gray"
    )
    self.polish_status_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

    # 保存路径设置
    ctk.CTkLabel(status_frame, text="保存路径:", font=("Microsoft YaHei", 12)).grid(
        row=0, column=2, padx=(20, 5), pady=5, sticky="w"
    )
    self.polish_save_path_var = ctk.StringVar(value="polished_output")
    self.polish_save_path_entry = ctk.CTkEntry(
        status_frame,
        textvariable=self.polish_save_path_var,
        placeholder_text="输入保存文件夹路径...",
        font=("Microsoft YaHei", 11)
    )
    self.polish_save_path_entry.grid(row=0, column=3, padx=5, pady=5, sticky="ew")

    # 浏览按钮
    def browse_save_path():
        from tkinter import filedialog
        folder_path = filedialog.askdirectory(title="选择保存文件夹")
        if folder_path:
            self.polish_save_path_var.set(folder_path)

    ctk.CTkButton(
        status_frame,
        text="浏览",
        width=60,
        command=browse_save_path,
        font=("Microsoft YaHei", 11)
    ).grid(row=0, column=4, padx=5, pady=5, sticky="w")

    # AI味评分显示
    score_frame = ctk.CTkFrame(content_frame)
    score_frame.grid(row=3, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
    score_frame.grid_columnconfigure(1, weight=1)
    score_frame.grid_columnconfigure(3, weight=1)

    ctk.CTkLabel(score_frame, text="原文AI味评分:", font=("Microsoft YaHei", 12)).grid(
        row=0, column=0, padx=5, pady=5, sticky="w"
    )
    self.original_ai_score_label = ctk.CTkLabel(
        score_frame, text="未分析", font=("Microsoft YaHei", 12, "bold")
    )
    self.original_ai_score_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

    ctk.CTkLabel(score_frame, text="润色后AI味评分:", font=("Microsoft YaHei", 12)).grid(
        row=0, column=2, padx=5, pady=5, sticky="w"
    )
    self.polished_ai_score_label = ctk.CTkLabel(
        score_frame, text="未分析", font=("Microsoft YaHei", 12, "bold")
    )
    self.polished_ai_score_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

def build_polish_action_area(self):
    """构建操作按钮区域"""
    action_frame = ctk.CTkFrame(self.polish_main_frame)
    action_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
    action_frame.grid_columnconfigure(0, weight=1)
    action_frame.grid_columnconfigure(1, weight=1)
    action_frame.grid_columnconfigure(2, weight=1)
    action_frame.grid_columnconfigure(3, weight=1)

    # 第一行按钮
    ctk.CTkButton(
        action_frame,
        text="加载章节文件",
        command=lambda: load_chapter_for_polish(self),
        font=("Microsoft YaHei", 12)
    ).grid(row=0, column=0, padx=5, pady=5, sticky="ew")

    ctk.CTkButton(
        action_frame,
        text="分析AI味",
        command=lambda: analyze_ai_traits(self),
        font=("Microsoft YaHei", 12)
    ).grid(row=0, column=1, padx=5, pady=5, sticky="ew")

    ctk.CTkButton(
        action_frame,
        text="开始润色",
        command=lambda: start_polish_process(self),
        font=("Microsoft YaHei", 12)
    ).grid(row=0, column=2, padx=5, pady=5, sticky="ew")

    ctk.CTkButton(
        action_frame,
        text="保存润色结果",
        command=lambda: save_polished_content(self),
        font=("Microsoft YaHei", 12)
    ).grid(row=0, column=3, padx=5, pady=5, sticky="ew")

    # 第二行按钮
    ctk.CTkButton(
        action_frame,
        text="批量润色章节",
        command=lambda: batch_polish_dialog(self),
        font=("Microsoft YaHei", 12)
    ).grid(row=1, column=0, padx=5, pady=5, sticky="ew")

    ctk.CTkButton(
        action_frame,
        text="清空内容",
        command=lambda: clear_polish_content(self),
        font=("Microsoft YaHei", 12)
    ).grid(row=1, column=1, padx=5, pady=5, sticky="ew")

    ctk.CTkButton(
        action_frame,
        text="导入风格样本",
        command=lambda: import_style_sample(self),
        font=("Microsoft YaHei", 12)
    ).grid(row=1, column=2, padx=5, pady=5, sticky="ew")

    ctk.CTkButton(
        action_frame,
        text="质量评估",
        command=lambda: assess_polish_quality(self),
        font=("Microsoft YaHei", 12)
    ).grid(row=1, column=3, padx=5, pady=5, sticky="ew")



# 事件处理函数
def on_polish_level_changed(value):
    """润色级别改变时的回调"""
    level_descriptions = {
        PolishLevel.LIGHT: "轻度润色：主要修正语言表达问题",
        PolishLevel.MEDIUM: "中度润色：改善表达并增加细节",
        PolishLevel.DEEP: "深度润色：全面重写以达到人类作者水准"
    }
    # 这里可以添加提示信息显示
    pass

def on_polish_model_changed(model_name: str):
    """当润色模型改变时保存配置"""
    if not model_name or model_name in ["无可用模型", "加载失败"]:
        return  # 不保存无效值

    try:
        from config_manager import load_polish_config, save_polish_config
        polish_config = load_polish_config("config.json")
        polish_config["polish_model"] = model_name
        save_polish_config("config.json", polish_config)
    except Exception as e:
        print(f"保存润色模型选择失败: {e}")

def browse_style_reference_file(self):
    """浏览风格参考文件"""
    file_path = filedialog.askopenfilename(
        title="选择风格参考文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
    )
    if file_path:
        self.style_reference_var.set(file_path)

def load_chapter_for_polish(self):
    """加载章节文件进行润色"""
    file_path = filedialog.askopenfilename(
        title="选择要润色的章节文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
    )
    if file_path:
        try:
            content = read_file(file_path)
            self.original_text.delete("1.0", "end")
            self.original_text.insert("1.0", content)

            # 自动分析AI味
            analyze_ai_traits(self)

            messagebox.showinfo("成功", f"已加载文件: {os.path.basename(file_path)}")
        except Exception as e:
            messagebox.showerror("错误", f"加载文件失败: {str(e)}")

def analyze_ai_traits(self):
    """分析原文的AI味特征"""
    content = self.original_text.get("1.0", "end-1c")
    if not content.strip():
        messagebox.showwarning("警告", "请先输入或加载要分析的内容")
        return

    try:
        ai_score = AITraitDetector.calculate_ai_score(content)
        ai_traits = AITraitDetector.detect_ai_traits(content)

        # 更新AI味评分显示（使用百分比）
        score_text = f"{ai_score:.1%}"
        color = "red" if ai_score > 0.6 else "orange" if ai_score > 0.3 else "green"
        self.original_ai_score_label.configure(text=score_text, text_color=color)

        # 显示详细分析结果
        if ai_traits:
            traits_text = "\n".join([f"• {pattern}: {count}次" for pattern, count in ai_traits.items()])
            messagebox.showinfo("AI味分析结果",
                f"AI味评分: {score_text}\n\n检测到的AI特征:\n{traits_text}")
        else:
            messagebox.showinfo("AI味分析结果",
                f"AI味评分: {score_text}\n\n未检测到明显的AI特征")

    except Exception as e:
        messagebox.showerror("错误", f"分析失败: {str(e)}")

def start_polish_process(self):
    """开始润色处理"""
    content = self.original_text.get("1.0", "end-1c")
    if not content.strip():
        messagebox.showwarning("警告", "请先输入或加载要润色的内容")
        return

    # 获取配置参数
    polish_level = self.polish_level_var.get()
    style_reference_file = self.style_reference_var.get()
    user_guidance = self.user_guidance_entry.get()
    scene_context = self.scene_context_entry.get()

    # 读取风格参考
    style_reference = ""
    if style_reference_file and os.path.exists(style_reference_file):
        style_reference = read_file(style_reference_file)

    # 读取角色信息
    character_info = ""
    if hasattr(self, 'character_state_text') and self.character_state_text.get("1.0", "end-1c").strip():
        character_info = self.character_state_text.get("1.0", "end-1c")

    def polish_task():
        try:
            # 更新状态：开始润色
            self.master.after(0, lambda: self.update_polish_status("执行中...", "blue"))

            # 获取统一的模型配置
            interface_format, api_key, base_url, llm_model, temperature, max_tokens, timeout = get_polish_model_config(self)

            # 执行润色
            polished_content, analysis_report = polish_text_content(
                interface_format=interface_format,
                api_key=api_key,
                base_url=base_url,
                llm_model=llm_model,
                content=content,
                polish_level=polish_level,
                style_reference=style_reference,
                character_info=character_info,
                scene_context=scene_context,
                user_guidance=user_guidance,
                temperature=temperature,
                max_tokens=max_tokens,
                timeout=timeout
            )

            # 在主线程中更新UI
            self.master.after(0, lambda: update_polish_result(self, polished_content, analysis_report))

            # 更新状态：全部完成
            self.master.after(0, lambda: self.update_polish_status("完成", "green"))

        except Exception as e:
            self.master.after(0, lambda: self.update_polish_status("失败", "red"))
            self.master.after(0, lambda: messagebox.showerror("错误", f"润色失败: {str(e)}"))

    # 在后台线程中执行润色
    self.update_polish_status("执行中...", "blue")
    threading.Thread(target=polish_task, daemon=True).start()

def auto_save_polished_content(self, polished_content):
    """自动保存润色后的内容到指定文件夹"""
    try:
        import os
        from datetime import datetime

        # 获取保存路径
        save_path = self.polish_save_path_var.get().strip()
        if not save_path:
            save_path = "polished_output"

        # 确保目录存在
        os.makedirs(save_path, exist_ok=True)

        # 生成文件名（使用时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"polished_{timestamp}.txt"
        filepath = os.path.join(save_path, filename)

        # 保存文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(polished_content)

        print(f"润色结果已自动保存到: {filepath}")

    except Exception as e:
        print(f"自动保存失败: {e}")

def update_polish_result(self, polished_content, analysis_report):
    """更新润色结果到UI"""
    # 更新润色后的文本
    self.polished_text.delete("1.0", "end")
    self.polished_text.insert("1.0", polished_content)

    # 自动保存润色后的内容
    auto_save_polished_content(self, polished_content)

    # 更新AI味评分（使用百分比）
    if 'polished_ai_score' in analysis_report:
        score = analysis_report['polished_ai_score']
        score_text = f"{score:.1%}"
        color = "red" if score > 0.6 else "orange" if score > 0.3 else "green"
        self.polished_ai_score_label.configure(text=score_text, text_color=color)

    # 显示改进效果
    if 'improvement' in analysis_report:
        improvement = analysis_report['improvement']
        if improvement > 0:
            messagebox.showinfo("润色完成",
                f"润色完成！\nAI味改善: {improvement:.2f}\n原文长度: {analysis_report.get('original_length', 0)}\n润色后长度: {analysis_report.get('polished_length', 0)}")
        else:
            messagebox.showinfo("润色完成", "润色完成！")

def save_polished_content(self):
    """保存润色后的内容"""
    content = self.polished_text.get("1.0", "end-1c")
    if not content.strip():
        messagebox.showwarning("警告", "没有润色后的内容可保存")
        return

    file_path = filedialog.asksaveasfilename(
        title="保存润色后的内容",
        defaultextension=".txt",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
    )
    if file_path:
        try:
            save_string_to_txt(content, file_path)
            messagebox.showinfo("成功", f"润色后的内容已保存到: {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

def clear_polish_content(self):
    """清空润色内容"""
    self.original_text.delete("1.0", "end")
    self.polished_text.delete("1.0", "end")
    self.original_ai_score_label.configure(text="未分析", text_color="gray")
    self.polished_ai_score_label.configure(text="未分析", text_color="gray")
    self.user_guidance_entry.delete(0, "end")
    self.scene_context_entry.delete(0, "end")
    # 重置润色状态
    self.update_polish_status("待执行", "gray")

def import_style_sample(self):
    """导入风格样本"""
    file_path = filedialog.askopenfilename(
        title="选择风格样本文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
    )
    if file_path:
        try:
            content = read_file(file_path)
            # 分析风格样本的特征
            ai_score = AITraitDetector.calculate_ai_score(content)

            # 显示样本分析结果
            messagebox.showinfo("风格样本分析",
                f"样本文件: {os.path.basename(file_path)}\n"
                f"AI味评分: {ai_score:.1%}\n"
                f"文本长度: {len(content)}字\n\n"
                f"该样本{'适合' if ai_score < 0.3 else '不太适合'}作为风格参考")

            # 设置为风格参考
            self.style_reference_var.set(file_path)

        except Exception as e:
            messagebox.showerror("错误", f"导入样本失败: {str(e)}")

def assess_polish_quality(self):
    """评估润色质量"""
    original_content = self.original_text.get("1.0", "end-1c")
    polished_content = self.polished_text.get("1.0", "end-1c")

    if not original_content.strip() or not polished_content.strip():
        messagebox.showwarning("警告", "请先完成润色后再进行质量评估")
        return

    def quality_task():
        try:
            # 更新状态：开始评估
            self.master.after(0, lambda: self.update_polish_status("执行中...", "blue"))

            # 计算改进指标
            original_score = AITraitDetector.calculate_ai_score(original_content)
            polished_score = AITraitDetector.calculate_ai_score(polished_content)
            improvement = original_score - polished_score

            # 计算长度变化
            length_change = len(polished_content) - len(original_content)
            length_change_percent = (length_change / len(original_content)) * 100

            # 生成评估报告
            report = f"""润色质量评估报告

原文AI味评分: {original_score:.1%}
润色后AI味评分: {polished_score:.1%}
AI味改善程度: {improvement:.1%} ({'提升' if improvement > 0 else '下降' if improvement < 0 else '无变化'})

原文长度: {len(original_content)}字
润色后长度: {len(polished_content)}字
长度变化: {length_change:+d}字 ({length_change_percent:+.1f}%)

质量评级: {get_quality_rating(improvement, length_change_percent)}
"""

            # 更新状态：评估完成
            self.master.after(0, lambda: self.update_polish_status("完成", "green"))
            self.master.after(0, lambda: messagebox.showinfo("质量评估", report))

        except Exception as e:
            self.master.after(0, lambda: self.update_polish_status("失败", "red"))
            self.master.after(0, lambda: messagebox.showerror("错误", f"质量评估失败: {str(e)}"))

    # 更新状态并启动任务
    self.update_polish_status("执行中...", "blue")
    threading.Thread(target=quality_task, daemon=True).start()

def get_quality_rating(improvement, length_change_percent):
    """根据改进程度和长度变化获取质量评级"""
    if improvement > 0.2:
        return "优秀 - AI味显著降低"
    elif improvement > 0.1:
        return "良好 - AI味有所改善"
    elif improvement > 0:
        return "一般 - 略有改善"
    elif improvement == 0:
        return "无变化"
    else:
        return "需要改进 - 质量下降"

def batch_polish_dialog(self):
    """批量润色对话框"""
    dialog = ctk.CTkToplevel(self.master)
    dialog.title("批量润色章节")
    dialog.geometry("500x400")
    dialog.transient(self.master)
    dialog.grab_set()

    # 主框架
    main_frame = ctk.CTkFrame(dialog)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    main_frame.grid_columnconfigure(0, weight=1)
    main_frame.grid_rowconfigure(2, weight=1)

    # 章节目录选择
    ctk.CTkLabel(main_frame, text="章节目录:", font=("Microsoft YaHei", 12)).grid(
        row=0, column=0, sticky="w", pady=5
    )

    dir_frame = ctk.CTkFrame(main_frame)
    dir_frame.grid(row=1, column=0, sticky="ew", pady=5)
    dir_frame.grid_columnconfigure(0, weight=1)

    chapters_dir_var = ctk.StringVar()
    chapters_dir_entry = ctk.CTkEntry(
        dir_frame,
        textvariable=chapters_dir_var,
        placeholder_text="选择章节目录..."
    )
    chapters_dir_entry.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

    def browse_chapters_dir():
        dir_path = filedialog.askdirectory(title="选择章节目录")
        if dir_path:
            chapters_dir_var.set(dir_path)
            update_chapter_list()

    ctk.CTkButton(
        dir_frame,
        text="浏览",
        width=60,
        command=browse_chapters_dir
    ).grid(row=0, column=1, padx=5, pady=5)

    # 章节列表
    ctk.CTkLabel(main_frame, text="选择要润色的章节:", font=("Microsoft YaHei", 12)).grid(
        row=2, column=0, sticky="w", pady=(10, 5)
    )

    # 创建滚动框架用于章节选择
    scrollable_frame = ctk.CTkScrollableFrame(main_frame, height=200)
    scrollable_frame.grid(row=3, column=0, sticky="ew", pady=5)

    chapter_vars = {}

    def update_chapter_list():
        # 清空现有的复选框
        for widget in scrollable_frame.winfo_children():
            widget.destroy()
        chapter_vars.clear()

        chapters_dir = chapters_dir_var.get()
        if not chapters_dir or not os.path.exists(chapters_dir):
            return

        # 查找章节文件
        chapter_files = []
        for file in os.listdir(chapters_dir):
            if file.startswith("chapter_") and file.endswith(".txt"):
                try:
                    chapter_num = int(file.replace("chapter_", "").replace(".txt", ""))
                    chapter_files.append(chapter_num)
                except ValueError:
                    continue

        chapter_files.sort()

        # 创建复选框
        for i, chapter_num in enumerate(chapter_files):
            var = ctk.BooleanVar()
            chapter_vars[chapter_num] = var

            checkbox = ctk.CTkCheckBox(
                scrollable_frame,
                text=f"第{chapter_num}章",
                variable=var
            )
            checkbox.grid(row=i//3, column=i%3, padx=10, pady=5, sticky="w")

    # 操作按钮
    button_frame = ctk.CTkFrame(main_frame)
    button_frame.grid(row=4, column=0, sticky="ew", pady=10)
    button_frame.grid_columnconfigure(0, weight=1)
    button_frame.grid_columnconfigure(1, weight=1)
    button_frame.grid_columnconfigure(2, weight=1)

    def select_all():
        for var in chapter_vars.values():
            var.set(True)

    def select_none():
        for var in chapter_vars.values():
            var.set(False)

    def start_batch_polish():
        selected_chapters = [num for num, var in chapter_vars.items() if var.get()]
        if not selected_chapters:
            messagebox.showwarning("警告", "请至少选择一个章节")
            return

        chapters_dir = chapters_dir_var.get()
        if not chapters_dir:
            messagebox.showwarning("警告", "请选择章节目录")
            return

        dialog.destroy()

        # 执行批量润色
        def batch_task():
            try:
                # 更新状态：开始批量润色
                self.master.after(0, lambda: self.update_polish_status("执行中...", "blue"))

                # 获取统一的模型配置
                interface_format, api_key, base_url, llm_model, temperature, max_tokens, timeout = get_polish_model_config(self)

                results = batch_polish_chapters(
                    interface_format=interface_format,
                    api_key=api_key,
                    base_url=base_url,
                    llm_model=llm_model,
                    chapters_dir=chapters_dir,
                    chapter_numbers=selected_chapters,
                    polish_level=self.polish_level_var.get(),
                    style_reference_file=self.style_reference_var.get(),
                    user_guidance=self.user_guidance_entry.get(),
                    scene_context=self.scene_context_entry.get(),
                    temperature=temperature,
                    max_tokens=max_tokens,
                    timeout=timeout
                )

                success_count = sum(1 for success in results.values() if success)
                total_count = len(results)

                # 更新状态：批量润色完成
                if success_count == total_count:
                    self.master.after(0, lambda: self.update_polish_status("完成", "green"))
                elif success_count > 0:
                    self.master.after(0, lambda: self.update_polish_status(f"部分完成 ({success_count}/{total_count})", "orange"))
                else:
                    self.master.after(0, lambda: self.update_polish_status("失败", "red"))

                self.master.after(0, lambda: messagebox.showinfo("批量润色完成",
                    f"批量润色完成！\n成功: {success_count}/{total_count}章"))

            except Exception as e:
                self.master.after(0, lambda: self.update_polish_status("失败", "red"))
                self.master.after(0, lambda: messagebox.showerror("错误", f"批量润色失败: {str(e)}"))

        # 更新状态并启动任务
        self.update_polish_status("执行中...", "blue")
        threading.Thread(target=batch_task, daemon=True).start()
        messagebox.showinfo("提示", f"开始批量润色{len(selected_chapters)}章，请稍候...")

    ctk.CTkButton(button_frame, text="全选", command=select_all).grid(row=0, column=0, padx=5, pady=5)
    ctk.CTkButton(button_frame, text="全不选", command=select_none).grid(row=0, column=1, padx=5, pady=5)
    ctk.CTkButton(button_frame, text="开始润色", command=start_batch_polish).grid(row=0, column=2, padx=5, pady=5)



def sync_polish_model_ui(self):
    """
    从配置文件同步润色模型的UI状态。
    - 从配置文件获取最新的模型列表。
    - 从配置文件加载并设置上次选中的模型。
    """
    try:
        from config_manager import load_config, load_polish_config

        # 1. 从配置文件获取最新的模型列表
        config_data = load_config("config.json")
        available_models = ["无可用模型"]

        # 获取当前选择的接口格式
        current_interface = self.interface_format_var.get().lower()

        # 从polish_configs中获取模型列表
        if (config_data and "polish_configs" in config_data and
            current_interface in config_data["polish_configs"] and
            "available_models" in config_data["polish_configs"][current_interface]):
            models = config_data["polish_configs"][current_interface]["available_models"]
            if models and isinstance(models, list) and len(models) > 0:
                # 过滤掉空字符串
                available_models = [m for m in models if m and m.strip()]
                if not available_models:
                    available_models = ["无可用模型"]

        # 更新模型下拉列表
        self.polish_model_menu.configure(values=available_models)

        # 2. 从配置文件加载上次保存的模型
        polish_config = load_polish_config("config.json")

        # 优先使用 polish_model, 其次是 polish_model_name
        last_selected_model = polish_config.get("polish_model")
        if not last_selected_model:
            last_selected_model = polish_config.get("polish_model_name")

        # 3. 检查模型是否存在并设置
        if last_selected_model and last_selected_model in available_models:
            self.polish_model_var.set(last_selected_model)
        elif available_models and available_models != ["无可用模型"]:
            # 如果保存的模型无效，则默认选择列表中的第一个
            self.polish_model_var.set(available_models[0])
        else:
            self.polish_model_var.set("无可用模型")

    except Exception as e:
        print(f"同步润色模型UI失败: {e}")
        self.polish_model_menu.configure(values=["加载失败"])
        self.polish_model_var.set("加载失败")


# 将方法绑定到主窗口类
def bind_polish_methods(self):
    """绑定润色相关方法到主窗口"""
    def update_polish_status(status, color="gray"):
        """更新润色状态显示

        Args:
            status: 状态文字 ("待执行", "执行中...", "完成", "失败")
            color: 状态颜色 ("gray", "blue", "green", "red")
        """
        if hasattr(self, 'polish_status_label'):
            self.polish_status_label.configure(text=status, text_color=color)

    self.update_polish_status = update_polish_status
    self.sync_polish_model_ui = lambda: sync_polish_model_ui(self)

    # 执行延迟的配置更新
    try:
        toggle_dedicated_model_config(self)
    except:
        pass  # 忽略初始化时的错误


def toggle_dedicated_model_config(self):
    """切换专用模型配置状态"""
    use_dedicated = self.use_dedicated_model_var.get()

    # 启用/禁用相关控件
    state = "normal" if use_dedicated else "disabled"
    self.polish_model_menu.configure(state=state)
    self.config_polish_model_btn.configure(state=state)

    # 如果启用，则更新模型列表
    if use_dedicated:
        self.sync_polish_model_ui()

    # 保存配置
    try:
        from config_manager import load_polish_config, save_polish_config
        polish_config = load_polish_config("config.json")
        polish_config["use_dedicated_model"] = use_dedicated
        save_polish_config("config.json", polish_config)
    except Exception as e:
        print(f"保存专用模型配置失败: {e}")

def configure_polish_model(self):
    """配置专用润色模型"""
    # 创建配置对话框
    config_window = ctk.CTkToplevel(self.master)
    config_window.title("配置专用润色模型")
    config_window.geometry("600x500")
    config_window.transient(self.master)
    config_window.grab_set()

    # 加载当前配置
    try:
        from config_manager import load_config, load_polish_config, save_polish_config, get_available_models
        polish_config = load_polish_config("config.json")

        # 加载完整配置以获取按接口分类的配置
        full_config = load_config(self.config_file)
        last_polish_interface = full_config.get("last_polish_interface_format", self.interface_format_var.get()) if full_config else self.interface_format_var.get()

    except Exception:
        polish_config = {}
        full_config = {}
        last_polish_interface = self.interface_format_var.get()

    # 创建配置表单
    form_frame = ctk.CTkFrame(config_window)
    form_frame.pack(fill="both", expand=True, padx=10, pady=10)

    # 接口格式
    ctk.CTkLabel(form_frame, text="接口格式:", font=("Microsoft YaHei", 12)).grid(
        row=0, column=0, padx=5, pady=5, sticky="w"
    )
    interface_var = ctk.StringVar(value=last_polish_interface)

    # 接口切换处理函数将在变量定义后重新定义

    # 先创建接口菜单，稍后设置command
    interface_menu = ctk.CTkOptionMenu(
        form_frame,
        variable=interface_var,
        values=["openai", "deepseek", "gemini", "azure", "ollama", "硅基流动"]
    )
    interface_menu.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

    # 获取当前接口的配置
    def get_interface_config(interface_name):
        """获取指定接口的配置"""
        if full_config and "polish_configs" in full_config and interface_name in full_config["polish_configs"]:
            return full_config["polish_configs"][interface_name]
        elif full_config and "llm_configs" in full_config and interface_name in full_config["llm_configs"]:
            # 如果没有专用润色配置，使用主配置
            return full_config["llm_configs"][interface_name]
        else:
            # 返回默认值
            return {
                "api_key": self.api_key_var.get(),
                "base_url": self.base_url_var.get(),
                "model_name": self.model_name_var.get(),
                "temperature": self.temperature_var.get(),
                "max_tokens": self.max_tokens_var.get(),
                "timeout": self.timeout_var.get()
            }

    current_config = get_interface_config(last_polish_interface)

    # API密钥
    ctk.CTkLabel(form_frame, text="API密钥:", font=("Microsoft YaHei", 12)).grid(
        row=1, column=0, padx=5, pady=5, sticky="w"
    )
    api_key_var = ctk.StringVar(value=current_config.get("api_key", ""))
    api_key_entry = ctk.CTkEntry(form_frame, textvariable=api_key_var, show="*")
    api_key_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

    # 基础URL
    ctk.CTkLabel(form_frame, text="基础URL:", font=("Microsoft YaHei", 12)).grid(
        row=2, column=0, padx=5, pady=5, sticky="w"
    )
    base_url_var = ctk.StringVar(value=current_config.get("base_url", ""))
    base_url_entry = ctk.CTkEntry(form_frame, textvariable=base_url_var)
    base_url_entry.grid(row=2, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

    # 状态标签
    status_label = ctk.CTkLabel(form_frame, text="", font=("Microsoft YaHei", 10))
    status_label.grid(row=2, column=3, padx=5, pady=5, sticky="w")

    # 模型名称（改为下拉选择）
    ctk.CTkLabel(form_frame, text="模型名称:", font=("Microsoft YaHei", 12)).grid(
        row=3, column=0, padx=5, pady=5, sticky="w"
    )
    model_var = ctk.StringVar(value=current_config.get("model_name", ""))

    # 加载保存的模型列表，如果没有则使用默认值
    saved_models = current_config.get("available_models", ["gpt-4", "gpt-3.5-turbo", "gemini-pro", "claude-3-sonnet"])
    model_menu = ctk.CTkOptionMenu(
        form_frame,
        variable=model_var,
        values=saved_models
    )
    model_menu.grid(row=3, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

    # 温度 - 与主配置保持一致 (0.0 - 2.0)
    ctk.CTkLabel(form_frame, text="Temperature:", font=("Microsoft YaHei", 12)).grid(
        row=4, column=0, padx=5, pady=5, sticky="w"
    )
    temp_var = ctk.DoubleVar(value=current_config.get("temperature", 0.7))
    temp_slider = ctk.CTkSlider(form_frame, from_=0.0, to=2.0, number_of_steps=200, variable=temp_var)
    temp_slider.grid(row=4, column=1, padx=5, pady=5, sticky="ew")
    temp_label = ctk.CTkLabel(form_frame, text=f"{temp_var.get():.2f}")
    temp_label.grid(row=4, column=2, padx=5, pady=5)

    # 更新温度显示
    def update_temp_label(*args):
        temp_label.configure(text=f"{temp_var.get():.2f}")
    temp_var.trace("w", update_temp_label)

    # 最大令牌数 - 与主配置保持一致 (0 - 102400)
    ctk.CTkLabel(form_frame, text="Max Tokens:", font=("Microsoft YaHei", 12)).grid(
        row=5, column=0, padx=5, pady=5, sticky="w"
    )
    tokens_var = ctk.IntVar(value=current_config.get("max_tokens", 4000))
    tokens_slider = ctk.CTkSlider(form_frame, from_=0, to=102400, number_of_steps=100, variable=tokens_var)
    tokens_slider.grid(row=5, column=1, padx=5, pady=5, sticky="ew")
    tokens_label = ctk.CTkLabel(form_frame, text=str(tokens_var.get()))
    tokens_label.grid(row=5, column=2, padx=5, pady=5)

    # 更新令牌数显示
    def update_tokens_label(*args):
        tokens_label.configure(text=str(int(tokens_var.get())))
    tokens_var.trace("w", update_tokens_label)

    # 超时时间 - 与主配置保持一致 (0 - 3600)
    ctk.CTkLabel(form_frame, text="Timeout (sec):", font=("Microsoft YaHei", 12)).grid(
        row=6, column=0, padx=5, pady=5, sticky="w"
    )
    timeout_var = ctk.IntVar(value=current_config.get("timeout", 120))
    timeout_slider = ctk.CTkSlider(form_frame, from_=0, to=3600, number_of_steps=3600, variable=timeout_var)
    timeout_slider.grid(row=6, column=1, padx=5, pady=5, sticky="ew")
    timeout_label = ctk.CTkLabel(form_frame, text=str(timeout_var.get()))
    timeout_label.grid(row=6, column=2, padx=5, pady=5)

    # 更新超时时间显示
    def update_timeout_label(*args):
        timeout_label.configure(text=str(int(timeout_var.get())))
    timeout_var.trace("w", update_timeout_label)

    # 配置列权重
    form_frame.grid_columnconfigure(1, weight=1)

    # 接口格式映射
    interface_mapping = {
        "openai": "openai",
        "gemini": "gemini",
        "azure": "azure",
        "ollama": "ollama"
    }

    # 创建自动加载器
    def get_models_wrapper(interface, api_key, base_url):
        from config_manager import get_available_models
        mapped_interface = interface_mapping.get(interface, "openai")
        return get_available_models(mapped_interface, api_key, base_url)

    def update_models_ui(models, from_cache=False):
        def update():
            model_menu.configure(values=models)
            if models and model_var.get() not in models:
                model_var.set(models[0])
            polish_loading_status.show_success(len(models), from_cache)
        config_window.after(0, update)

    def show_status(status):
        # 不显示"加载中..."状态，只显示错误信息
        if status and "加载中" not in status:
            config_window.after(0, lambda: status_label.configure(text=status))

    def show_error(error):
        config_window.after(0, lambda: status_label.configure(text=error))

    # 创建加载状态管理器
    polish_loading_status = ModelLoadingStatus(
        status_label=status_label,
        load_button=None,
        button_text="加载模型"
    )

    # 创建自动加载器
    polish_auto_loader = AutoModelLoader(
        get_models_func=get_models_wrapper,
        update_ui_func=update_models_ui,
        show_status_func=show_status,
        show_error_func=show_error
    )

    # 现在定义接口切换处理函数
    def on_polish_interface_changed(new_value):
        """处理润色接口格式变化"""
        interface_var.set(new_value)

        # 加载对应接口的配置
        from config_manager import load_config, save_config
        config_data = load_config("config.json")

        if config_data and "polish_configs" in config_data and new_value in config_data["polish_configs"]:
            # 如果有保存的配置，加载它
            polish_conf = config_data["polish_configs"][new_value]
            api_key_var.set(polish_conf.get("api_key", ""))
            base_url_var.set(polish_conf.get("base_url", ""))
            model_var.set(polish_conf.get("model_name", ""))
            temp_var.set(polish_conf.get("temperature", 0.7))
            tokens_var.set(polish_conf.get("max_tokens", 4000))
            timeout_var.set(polish_conf.get("timeout", 120))
        else:
            # 如果没有保存的配置，使用Main Functions的配置作为默认值
            # 需要将小写的接口名称映射到Main Functions中的大写格式
            interface_mapping = {
                "openai": "OpenAI",
                "deepseek": "DeepSeek",
                "gemini": "Gemini",
                "azure": "Azure",
                "ollama": "Ollama"
            }
            main_interface_key = interface_mapping.get(new_value, new_value.capitalize())

            if config_data and "llm_configs" in config_data and main_interface_key in config_data["llm_configs"]:
                main_conf = config_data["llm_configs"][main_interface_key]
                api_key_var.set(main_conf.get("api_key", ""))
                base_url_var.set(main_conf.get("base_url", ""))
                model_var.set(main_conf.get("model_name", ""))
                temp_var.set(main_conf.get("temperature", 0.7))
                tokens_var.set(main_conf.get("max_tokens", 4000))
                timeout_var.set(main_conf.get("timeout", 120))
            else:
                # 如果Main Functions中也没有配置，清空字段
                api_key_var.set("")
                base_url_var.set("")
                model_var.set("")
                temp_var.set(0.7)
                tokens_var.set(4000)
                timeout_var.set(120)

        # 保存当前选择的接口
        if not config_data:
            config_data = {}
        config_data["last_polish_interface_format"] = new_value
        save_config(config_data, "config.json")

        # 触发模型列表加载
        load_models_for_interface()

        # 同步主界面的接口格式选择
        self.interface_format_var.set(new_value)

    # 设置接口菜单的command
    interface_menu.configure(command=on_polish_interface_changed)

    # 自动触发机制
    def trigger_auto_load(*args):
        interface = interface_var.get()
        api_key = api_key_var.get()
        base_url = base_url_var.get()
        polish_auto_loader.trigger_auto_load(interface, api_key, base_url)

    # 手动加载函数
    def load_models_for_interface():
        interface = interface_var.get()
        api_key = api_key_var.get()
        base_url = base_url_var.get()
        polish_auto_loader.manual_load(interface, api_key, base_url)

    # 绑定自动触发事件
    def on_interface_changed(value):
        """处理接口切换，包括配置切换和模型加载"""
        # 调用完整的接口切换处理函数
        on_polish_interface_changed(value)

    def on_api_key_focus_out(event):
        trigger_auto_load()

    def on_base_url_focus_out(event):
        trigger_auto_load()

    # 更新接口格式下拉菜单的命令
    interface_menu.configure(command=on_interface_changed)

    # 绑定输入框事件
    api_key_entry.bind("<FocusOut>", on_api_key_focus_out)
    api_key_entry.bind("<Return>", on_api_key_focus_out)
    base_url_entry.bind("<FocusOut>", on_base_url_focus_out)
    base_url_entry.bind("<Return>", on_base_url_focus_out)

    # 按钮区域
    button_frame = ctk.CTkFrame(config_window)
    button_frame.pack(fill="x", padx=10, pady=5)

    def save_config():
        """保存配置"""
        try:
            from config_manager import load_config, save_config as save_config_file

            # 获取当前模型列表
            current_models = list(model_menu.cget("values"))
            current_interface = interface_var.get()

            # 更新润色配置
            polish_config.update({
                "polish_interface_format": current_interface,
                "polish_api_key": api_key_var.get(),
                "polish_base_url": base_url_var.get(),
                "polish_model_name": model_var.get(),
                "polish_temperature": temp_var.get(),
                "polish_max_tokens": int(tokens_var.get()),
                "polish_timeout": int(timeout_var.get()),
                "polish_available_models": current_models  # 保存模型列表
            })

            # 加载现有配置
            config_data = load_config(self.config_file)
            if not config_data:
                config_data = {}

            # 保存润色配置
            config_data["polish_config"] = polish_config

            # 按接口格式保存专用润色配置
            if "polish_configs" not in config_data:
                config_data["polish_configs"] = {}

            config_data["polish_configs"][current_interface] = {
                "api_key": api_key_var.get(),
                "base_url": base_url_var.get(),
                "model_name": model_var.get(),
                "temperature": temp_var.get(),
                "max_tokens": int(tokens_var.get()),
                "timeout": int(timeout_var.get()),
                "available_models": current_models
            }

            # 保存最后选择的润色接口
            config_data["last_polish_interface_format"] = current_interface

            # 保存到文件
            save_config_file(config_data, "config.json")

            # 更新主界面的模型选择
            self.sync_polish_model_ui()

            messagebox.showinfo("成功", "专用润色模型配置已保存")
            config_window.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def test_config():
        """测试配置"""
        def test_task():
            try:
                from config_manager import test_polish_config

                # 显示测试中状态
                config_window.after(0, lambda: test_btn.configure(text="测试中..."))

                def log_func(msg):
                    print(msg)

                def handle_exception_func(msg):
                    config_window.after(0, lambda: messagebox.showerror("测试失败", msg))

                # 执行测试
                success = test_polish_config(
                    interface_format=interface_var.get(),
                    api_key=api_key_var.get(),
                    base_url=base_url_var.get(),
                    model_name=model_var.get(),
                    temperature=temp_var.get(),
                    max_tokens=int(tokens_var.get()),
                    timeout=int(timeout_var.get()),
                    log_func=log_func,
                    handle_exception_func=handle_exception_func
                )

                # 显示测试结果
                if success:
                    config_window.after(0, lambda: messagebox.showinfo("测试成功", "专用润色模型配置测试成功！"))
                else:
                    config_window.after(0, lambda: messagebox.showerror("测试失败", "专用润色模型配置测试失败！"))

                config_window.after(0, lambda: test_btn.configure(text="测试"))

            except Exception as e:
                config_window.after(0, lambda: messagebox.showerror("错误", f"测试配置失败: {str(e)}"))
                config_window.after(0, lambda: test_btn.configure(text="测试"))

        # 在后台线程中执行测试
        threading.Thread(target=test_task, daemon=True).start()

    ctk.CTkButton(button_frame, text="保存", command=save_config).pack(side="left", padx=5, pady=5)
    test_btn = ctk.CTkButton(button_frame, text="测试", command=test_config)
    test_btn.pack(side="left", padx=5, pady=5)
    load_models_btn = ctk.CTkButton(button_frame, text="加载模型", command=lambda: load_models_for_interface())
    load_models_btn.pack(side="left", padx=5, pady=5)
    ctk.CTkButton(button_frame, text="取消", command=config_window.destroy).pack(side="right", padx=5, pady=5)

    # 设置按钮到状态管理器
    polish_loading_status.set_button(load_models_btn, "加载模型")